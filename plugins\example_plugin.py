#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
示例游戏插件
演示如何创建自定义游戏插件
"""

from core.game_plugin_system import GamePlugin, GamePluginInfo
from core.image_recognizer import <PERSON>Recognizer
from core.game_automation import GameAutomationBase, GameAction, ActionType
from typing import Dict, List, Any, Type
import cv2
import numpy as np

class ExampleRecognizer(ImageRecognizer):
    """示例识别器"""
    
    def __init__(self):
        super().__init__()
        # 示例游戏特定参数
        self.game_specific_params = {
            'min_area': 100,
            'max_area': 10000
        }
        
    def recognize_example_game(self, image: np.ndarray) -> Dict[str, Any]:
        """识别示例游戏"""
        try:
            # 示例识别逻辑
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # 查找轮廓
            contours, _ = cv2.findContours(gray, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # 分析轮廓
            game_objects = []
            for contour in contours:
                area = cv2.contourArea(contour)
                if self.game_specific_params['min_area'] < area < self.game_specific_params['max_area']:
                    x, y, w, h = cv2.boundingRect(contour)
                    game_objects.append({
                        'position': (x, y),
                        'size': (w, h),
                        'area': area
                    })
                    
            return {
                'game_objects': game_objects,
                'object_count': len(game_objects),
                'game_type': 'example'
            }
            
        except Exception as e:
            return {'error': str(e), 'game_type': 'example'}

class ExampleAutomation(GameAutomationBase):
    """示例自动化"""
    
    def __init__(self):
        super().__init__("example")
        
    def analyze_game_state(self, recognition_result: Dict[str, Any]) -> Dict[str, Any]:
        """分析示例游戏状态"""
        try:
            if 'error' in recognition_result:
                return {'error': recognition_result['error']}
                
            game_objects = recognition_result.get('game_objects', [])
            
            # 分析游戏状态
            state = {
                'total_objects': len(game_objects),
                'objects': game_objects,
                'can_move': len(game_objects) > 0
            }
            
            return state
            
        except Exception as e:
            return {'error': str(e)}
            
    def generate_solution(self, game_state: Dict[str, Any]) -> List[GameAction]:
        """生成示例解决方案"""
        actions = []
        
        try:
            if 'error' in game_state:
                return actions
                
            objects = game_state.get('objects', [])
            
            # 示例：点击每个对象
            for i, obj in enumerate(objects):
                position = obj['position']
                size = obj['size']
                
                # 计算点击位置（对象中心）
                click_pos = (
                    position[0] + size[0] // 2,
                    position[1] + size[1] // 2
                )
                
                # 创建点击操作
                action = GameAction(
                    action_type=ActionType.CLICK,
                    position=click_pos,
                    description=f"点击对象 {i+1}",
                    delay_after=0.5
                )
                
                actions.append(action)
                
        except Exception as e:
            print(f"生成示例解决方案失败: {e}")
            
        return actions
        
    def validate_action(self, action: GameAction) -> bool:
        """验证示例操作"""
        if action.action_type == ActionType.CLICK:
            return action.position is not None
        elif action.action_type == ActionType.DRAG:
            return action.position is not None and action.target_position is not None
        return True

class ExamplePlugin(GamePlugin):
    """示例插件"""
    
    def get_plugin_info(self) -> GamePluginInfo:
        return GamePluginInfo(
            name="example",
            display_name="示例游戏",
            version="1.0.0",
            author="系统",
            description="这是一个示例游戏插件，演示如何创建自定义插件",
            supported_features=["recognition", "automation"]
        )
        
    def get_recognizer_class(self) -> Type[ImageRecognizer]:
        return ExampleRecognizer
        
    def get_automation_class(self) -> Type[GameAutomationBase]:
        return ExampleAutomation
        
    def get_config_schema(self) -> Dict[str, Any]:
        """获取配置模式"""
        return {
            'min_area': {
                'type': 'integer',
                'default': 100,
                'description': '最小区域面积'
            },
            'max_area': {
                'type': 'integer', 
                'default': 10000,
                'description': '最大区域面积'
            }
        }
        
    def get_supported_features(self) -> List[str]:
        return ["recognition", "automation", "configuration"]
        
    def validate_plugin(self) -> bool:
        """验证插件"""
        try:
            # 检查必要的依赖
            import cv2
            import numpy as np
            
            # 检查类是否正确实现
            recognizer = ExampleRecognizer()
            automation = ExampleAutomation()
            
            return True
            
        except Exception as e:
            print(f"示例插件验证失败: {e}")
            return False
