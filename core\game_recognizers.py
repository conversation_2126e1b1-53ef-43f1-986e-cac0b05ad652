#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
游戏特定的识别器
为不同游戏类型提供专门的识别逻辑
"""

import cv2
import numpy as np
from typing import List, Dict, Any, Tuple, Optional
from core.image_recognizer import ImageRecognizer

class SudokuRecognizer(ImageRecognizer):
    """数独识别器"""
    
    def __init__(self):
        super().__init__()
        # 数独特定参数
        self.grid_size = (9, 9)
        self.valid_digits = set('123456789')
        
    def recognize_sudoku_grid(self, image: np.ndarray) -> Dict[str, Any]:
        """识别数独网格"""
        try:
            # 增强图像质量
            enhanced = self.enhance_image_quality(image)
            
            # 识别网格
            grid_results = self.recognize_grid(enhanced, self.grid_size)
            
            # 转换为数独格式
            sudoku_grid = []
            for row in grid_results:
                sudoku_row = []
                for cell in row:
                    if cell['is_digit'] and cell['text'] in self.valid_digits:
                        sudoku_row.append(int(cell['text']))
                    else:
                        sudoku_row.append(0)  # 空白用0表示
                sudoku_grid.append(sudoku_row)
                
            # 验证数独有效性
            is_valid = self.validate_sudoku_grid(sudoku_grid)
            
            # 获取统计信息
            stats = self.get_recognition_stats(grid_results)
            
            return {
                'grid': sudoku_grid,
                'raw_results': grid_results,
                'is_valid': is_valid,
                'stats': stats,
                'game_type': 'sudoku'
            }
            
        except Exception as e:
            return {'error': str(e), 'game_type': 'sudoku'}
            
    def validate_sudoku_grid(self, grid: List[List[int]]) -> bool:
        """验证数独网格的有效性"""
        try:
            # 检查行
            for row in grid:
                non_zero = [x for x in row if x != 0]
                if len(non_zero) != len(set(non_zero)):
                    return False
                    
            # 检查列
            for col in range(9):
                column = [grid[row][col] for row in range(9)]
                non_zero = [x for x in column if x != 0]
                if len(non_zero) != len(set(non_zero)):
                    return False
                    
            # 检查3x3方块
            for box_row in range(3):
                for box_col in range(3):
                    box = []
                    for row in range(box_row * 3, (box_row + 1) * 3):
                        for col in range(box_col * 3, (box_col + 1) * 3):
                            box.append(grid[row][col])
                    non_zero = [x for x in box if x != 0]
                    if len(non_zero) != len(set(non_zero)):
                        return False
                        
            return True
            
        except Exception:
            return False

class NonogramRecognizer(ImageRecognizer):
    """数织识别器"""
    
    def __init__(self):
        super().__init__()
        # 数织特定参数
        self.valid_digits = set('0123456789')
        
    def recognize_nonogram(self, main_grid_image: np.ndarray, 
                          row_hints_image: np.ndarray, 
                          col_hints_image: np.ndarray,
                          grid_size: Tuple[int, int] = (10, 10)) -> Dict[str, Any]:
        """识别数织游戏"""
        try:
            rows, cols = grid_size
            
            # 识别主网格（已填充的方块）
            main_grid = self.recognize_filled_grid(main_grid_image, grid_size)
            
            # 识别行提示
            row_hints = self.recognize_hints(row_hints_image, rows, is_row=True)
            
            # 识别列提示
            col_hints = self.recognize_hints(col_hints_image, cols, is_row=False)
            
            return {
                'main_grid': main_grid,
                'row_hints': row_hints,
                'col_hints': col_hints,
                'grid_size': grid_size,
                'game_type': 'nonogram'
            }
            
        except Exception as e:
            return {'error': str(e), 'game_type': 'nonogram'}
            
    def recognize_filled_grid(self, image: np.ndarray, grid_size: Tuple[int, int]) -> List[List[int]]:
        """识别已填充的网格"""
        try:
            rows, cols = grid_size
            height, width = image.shape[:2]
            
            cell_width = width // cols
            cell_height = height // rows
            
            grid = []
            
            for row in range(rows):
                grid_row = []
                for col in range(cols):
                    # 提取单元格
                    x = col * cell_width
                    y = row * cell_height
                    cell = image[y:y+cell_height, x:x+cell_width]
                    
                    # 判断是否填充（黑色方块）
                    is_filled = self.is_cell_filled(cell)
                    grid_row.append(1 if is_filled else 0)
                    
                grid.append(grid_row)
                
            return grid
            
        except Exception as e:
            raise Exception(f"识别填充网格失败: {e}")
            
    def is_cell_filled(self, cell_image: np.ndarray, threshold: float = 0.5) -> bool:
        """判断单元格是否被填充"""
        try:
            # 转换为灰度图
            gray = cv2.cvtColor(cell_image, cv2.COLOR_BGR2GRAY) if len(cell_image.shape) == 3 else cell_image
            
            # 计算黑色像素比例
            black_pixels = np.sum(gray < 128)
            total_pixels = gray.size
            
            ratio = black_pixels / total_pixels
            
            return ratio > threshold
            
        except Exception:
            return False
            
    def recognize_hints(self, image: np.ndarray, hint_count: int, is_row: bool = True) -> List[List[int]]:
        """识别提示数字"""
        try:
            hints = []
            
            if is_row:
                # 行提示：每行有多个数字
                height, width = image.shape[:2]
                hint_height = height // hint_count
                
                for i in range(hint_count):
                    y = i * hint_height
                    hint_image = image[y:y+hint_height, :]
                    
                    # 识别这一行的所有数字
                    row_hints = self.extract_hint_numbers(hint_image)
                    hints.append(row_hints)
                    
            else:
                # 列提示：每列有多个数字（需要旋转处理）
                height, width = image.shape[:2]
                hint_width = width // hint_count
                
                for i in range(hint_count):
                    x = i * hint_width
                    hint_image = image[:, x:x+hint_width]
                    
                    # 旋转90度以便识别
                    rotated = cv2.rotate(hint_image, cv2.ROTATE_90_CLOCKWISE)
                    
                    # 识别这一列的所有数字
                    col_hints = self.extract_hint_numbers(rotated)
                    hints.append(col_hints)
                    
            return hints
            
        except Exception as e:
            raise Exception(f"识别提示失败: {e}")
            
    def extract_hint_numbers(self, image: np.ndarray) -> List[int]:
        """从提示图像中提取数字"""
        try:
            # 预处理图像
            processed = self.preprocess_image(image)
            
            # 查找轮廓（数字区域）
            contours, _ = cv2.findContours(processed, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # 按x坐标排序轮廓
            contours = sorted(contours, key=lambda c: cv2.boundingRect(c)[0])
            
            numbers = []
            
            for contour in contours:
                x, y, w, h = cv2.boundingRect(contour)
                
                # 过滤太小的轮廓
                if w < 5 or h < 5:
                    continue
                    
                # 提取数字区域
                digit_image = processed[y:y+h, x:x+w]
                
                # 识别数字
                result = self.recognize_digit(digit_image)
                
                if result['is_digit']:
                    numbers.append(int(result['text']))
                    
            return numbers
            
        except Exception:
            return []

class HuarongRecognizer(ImageRecognizer):
    """华容道识别器"""
    
    def __init__(self):
        super().__init__()
        # 华容道特定参数
        self.piece_types = {
            'cao_cao': '曹操',
            'guan_yu': '关羽', 
            'zhang_fei': '张飞',
            'zhao_yun': '赵云',
            'ma_chao': '马超',
            'huang_zhong': '黄忠',
            'soldier': '兵'
        }
        
    def recognize_huarong_board(self, image: np.ndarray, 
                               board_size: Tuple[int, int] = (5, 4)) -> Dict[str, Any]:
        """识别华容道棋盘"""
        try:
            rows, cols = board_size
            
            # 识别棋盘上的棋子
            board = self.recognize_pieces_grid(image, board_size)
            
            # 分析棋子位置
            piece_positions = self.analyze_piece_positions(board)
            
            return {
                'board': board,
                'piece_positions': piece_positions,
                'board_size': board_size,
                'game_type': 'huarong'
            }
            
        except Exception as e:
            return {'error': str(e), 'game_type': 'huarong'}
            
    def recognize_pieces_grid(self, image: np.ndarray, 
                             board_size: Tuple[int, int]) -> List[List[str]]:
        """识别棋子网格"""
        try:
            rows, cols = board_size
            height, width = image.shape[:2]
            
            cell_width = width // cols
            cell_height = height // rows
            
            board = []
            
            for row in range(rows):
                board_row = []
                for col in range(cols):
                    # 提取单元格
                    x = col * cell_width
                    y = row * cell_height
                    cell = image[y:y+cell_height, x:x+cell_width]
                    
                    # 识别棋子类型
                    piece_type = self.identify_piece_type(cell)
                    board_row.append(piece_type)
                    
                board.append(board_row)
                
            return board
            
        except Exception as e:
            raise Exception(f"识别棋子网格失败: {e}")
            
    def identify_piece_type(self, cell_image: np.ndarray) -> str:
        """识别棋子类型"""
        try:
            # 这里可以使用颜色分析、形状识别等方法
            # 简化实现：基于颜色特征
            
            # 转换为HSV颜色空间
            hsv = cv2.cvtColor(cell_image, cv2.COLOR_BGR2HSV)
            
            # 计算主要颜色
            dominant_color = self.get_dominant_color(cell_image)
            
            # 根据颜色判断棋子类型（这里需要根据实际游戏调整）
            if self.is_empty_cell(cell_image):
                return 'empty'
            elif self.is_red_piece(dominant_color):
                return 'cao_cao'  # 曹操通常是红色
            elif self.is_green_piece(dominant_color):
                return 'guan_yu'  # 关羽通常是绿色
            else:
                return 'soldier'  # 默认为兵
                
        except Exception:
            return 'unknown'
            
    def get_dominant_color(self, image: np.ndarray) -> Tuple[int, int, int]:
        """获取图像的主要颜色"""
        try:
            # 重塑图像为像素列表
            pixels = image.reshape(-1, 3)
            
            # 简化实现：计算平均颜色
            return tuple(map(int, np.mean(pixels, axis=0)))
            
        except Exception:
            # 如果K-means失败，使用简单的平均值
            return tuple(map(int, np.mean(image.reshape(-1, 3), axis=0)))
            
    def is_empty_cell(self, image: np.ndarray) -> bool:
        """判断是否为空单元格"""
        return self.detect_empty_cells(image, threshold=0.1)
        
    def is_red_piece(self, color: Tuple[int, int, int]) -> bool:
        """判断是否为红色棋子"""
        b, g, r = color
        return r > 150 and r > g * 1.5 and r > b * 1.5
        
    def is_green_piece(self, color: Tuple[int, int, int]) -> bool:
        """判断是否为绿色棋子"""
        b, g, r = color
        return g > 150 and g > r * 1.2 and g > b * 1.2
        
    def analyze_piece_positions(self, board: List[List[str]]) -> Dict[str, List[Tuple[int, int]]]:
        """分析棋子位置"""
        try:
            positions = {}
            
            for row in range(len(board)):
                for col in range(len(board[row])):
                    piece = board[row][col]
                    if piece != 'empty' and piece != 'unknown':
                        if piece not in positions:
                            positions[piece] = []
                        positions[piece].append((row, col))
                        
            return positions
            
        except Exception:
            return {}
