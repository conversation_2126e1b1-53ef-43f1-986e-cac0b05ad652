#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理面板
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import json
import os
from datetime import datetime

from utils.dpi_helper import DPIAwareWidget

class ConfigPanel(DPIAwareWidget):
    """配置管理面板类"""
    
    def __init__(self, parent, callback=None):
        super().__init__(parent)
        self.parent = parent
        self.callback = callback
        self.current_game = ""
        self.current_config = ""
        self.configs = {}
        self.create_widgets()
        self.load_configs()
        
    def create_widgets(self):
        """创建控件"""
        # 创建笔记本控件（标签页）
        self.notebook = ttk.Notebook(self.parent)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # 配置列表标签页
        self.create_config_list_tab()
        
        # 配置编辑标签页
        self.create_config_edit_tab()
        
        # 导入导出标签页
        self.create_import_export_tab()
        
    def create_config_list_tab(self):
        """创建配置列表标签页"""
        list_frame = ttk.Frame(self.notebook)
        self.notebook.add(list_frame, text="配置列表")
        
        # 游戏类型过滤
        filter_frame = ttk.Frame(list_frame)
        filter_frame.pack(fill=tk.X, pady=(0, self.scaled(10)))
        
        ttk.Label(filter_frame, text="游戏类型:").pack(side=tk.LEFT)
        self.game_filter_var = tk.StringVar(value="全部")
        self.game_filter_combo = ttk.Combobox(
            filter_frame,
            textvariable=self.game_filter_var,
            values=["全部", "数独", "数织", "华容道", "滑块拼图", "自定义"],
            state="readonly",
            width=10
        )
        self.game_filter_combo.pack(side=tk.LEFT, padx=(self.scaled(5), 0))
        self.game_filter_combo.bind('<<ComboboxSelected>>', self.filter_configs)
        
        # 配置列表
        list_container = ttk.Frame(list_frame)
        list_container.pack(fill=tk.BOTH, expand=True)
        
        # 创建Treeview
        columns = ("名称", "游戏类型", "创建时间", "最后使用")
        self.config_tree = ttk.Treeview(list_container, columns=columns, show="headings", height=10)
        
        # 设置列标题
        for col in columns:
            self.config_tree.heading(col, text=col)
            self.config_tree.column(col, width=self.scaled(120))
            
        # 添加滚动条
        tree_scroll = ttk.Scrollbar(list_container, orient=tk.VERTICAL, command=self.config_tree.yview)
        self.config_tree.configure(yscrollcommand=tree_scroll.set)
        
        self.config_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        tree_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 绑定选择事件
        self.config_tree.bind('<<TreeviewSelect>>', self.on_config_select)
        self.config_tree.bind('<Double-1>', self.edit_config)
        
        # 操作按钮
        button_frame = ttk.Frame(list_frame)
        button_frame.pack(fill=tk.X, pady=(self.scaled(10), 0))
        
        ttk.Button(button_frame, text="新建配置", command=self.new_config).pack(side=tk.LEFT, padx=(0, self.scaled(5)))
        ttk.Button(button_frame, text="编辑配置", command=self.edit_config).pack(side=tk.LEFT, padx=(0, self.scaled(5)))
        ttk.Button(button_frame, text="复制配置", command=self.copy_config).pack(side=tk.LEFT, padx=(0, self.scaled(5)))
        ttk.Button(button_frame, text="删除配置", command=self.delete_config).pack(side=tk.LEFT, padx=(0, self.scaled(5)))
        ttk.Button(button_frame, text="刷新列表", command=self.refresh_configs).pack(side=tk.RIGHT)
        
    def create_config_edit_tab(self):
        """创建配置编辑标签页"""
        edit_frame = ttk.Frame(self.notebook)
        self.notebook.add(edit_frame, text="配置编辑")
        
        # 基本信息
        basic_frame = ttk.LabelFrame(edit_frame, text="基本信息", padding=self.scaled_tuple(10, 10))
        basic_frame.pack(fill=tk.X, pady=(0, self.scaled(10)))
        
        # 配置名称
        ttk.Label(basic_frame, text="配置名称:").grid(row=0, column=0, sticky=tk.W, pady=self.scaled(2))
        self.config_name_var = tk.StringVar()
        ttk.Entry(basic_frame, textvariable=self.config_name_var, width=30).grid(row=0, column=1, sticky=tk.W, padx=(self.scaled(10), 0), pady=self.scaled(2))
        
        # 游戏类型
        ttk.Label(basic_frame, text="游戏类型:").grid(row=1, column=0, sticky=tk.W, pady=self.scaled(2))
        self.config_game_var = tk.StringVar()
        ttk.Combobox(basic_frame, textvariable=self.config_game_var, values=["数独", "数织", "华容道", "滑块拼图", "自定义"], state="readonly", width=27).grid(row=1, column=1, sticky=tk.W, padx=(self.scaled(10), 0), pady=self.scaled(2))
        
        # 描述
        ttk.Label(basic_frame, text="描述:").grid(row=2, column=0, sticky=tk.NW, pady=self.scaled(2))
        self.config_desc_text = tk.Text(basic_frame, width=40, height=3, font=('Microsoft YaHei UI', self.scaled(8)))
        self.config_desc_text.grid(row=2, column=1, sticky=tk.W, padx=(self.scaled(10), 0), pady=self.scaled(2))
        
        # 区域配置
        region_frame = ttk.LabelFrame(edit_frame, text="区域配置", padding=self.scaled_tuple(10, 10))
        region_frame.pack(fill=tk.BOTH, expand=True, pady=(0, self.scaled(10)))
        
        # 这里将在后续添加区域配置的详细控件
        ttk.Label(region_frame, text="区域配置将在框选功能完成后添加").pack()
        
        # 保存按钮
        save_frame = ttk.Frame(edit_frame)
        save_frame.pack(fill=tk.X)
        
        ttk.Button(save_frame, text="保存配置", command=self.save_config).pack(side=tk.RIGHT, padx=(self.scaled(5), 0))
        ttk.Button(save_frame, text="取消编辑", command=self.cancel_edit).pack(side=tk.RIGHT)
        
    def create_import_export_tab(self):
        """创建导入导出标签页"""
        ie_frame = ttk.Frame(self.notebook)
        self.notebook.add(ie_frame, text="导入导出")
        
        # 导出区域
        export_frame = ttk.LabelFrame(ie_frame, text="导出配置", padding=self.scaled_tuple(10, 10))
        export_frame.pack(fill=tk.X, pady=(0, self.scaled(10)))
        
        ttk.Button(export_frame, text="导出所有配置", command=self.export_all_configs).pack(side=tk.LEFT, padx=(0, self.scaled(5)))
        ttk.Button(export_frame, text="导出选中配置", command=self.export_selected_config).pack(side=tk.LEFT)
        
        # 导入区域
        import_frame = ttk.LabelFrame(ie_frame, text="导入配置", padding=self.scaled_tuple(10, 10))
        import_frame.pack(fill=tk.X, pady=(0, self.scaled(10)))
        
        ttk.Button(import_frame, text="导入配置文件", command=self.import_configs).pack(side=tk.LEFT, padx=(0, self.scaled(5)))
        ttk.Button(import_frame, text="从剪贴板导入", command=self.import_from_clipboard).pack(side=tk.LEFT)
        
        # 备份区域
        backup_frame = ttk.LabelFrame(ie_frame, text="备份管理", padding=self.scaled_tuple(10, 10))
        backup_frame.pack(fill=tk.X)
        
        ttk.Button(backup_frame, text="创建备份", command=self.create_backup).pack(side=tk.LEFT, padx=(0, self.scaled(5)))
        ttk.Button(backup_frame, text="恢复备份", command=self.restore_backup).pack(side=tk.LEFT)
        
    def set_game_type(self, game_type):
        """设置游戏类型"""
        self.current_game = game_type
        self.game_filter_var.set(self.get_game_display_name(game_type))
        self.filter_configs()
        
    def get_game_display_name(self, game_id):
        """获取游戏显示名称"""
        game_names = {
            "sudoku": "数独",
            "nonogram": "数织", 
            "huarong": "华容道",
            "klotski": "滑块拼图",
            "custom": "自定义"
        }
        return game_names.get(game_id, game_id)
        
    def filter_configs(self, event=None):
        """过滤配置列表"""
        # 清空当前列表
        for item in self.config_tree.get_children():
            self.config_tree.delete(item)
            
        # 获取过滤条件
        filter_game = self.game_filter_var.get()
        
        # 添加符合条件的配置
        for config_id, config_data in self.configs.items():
            if filter_game == "全部" or config_data.get("game_type", "") == filter_game:
                self.config_tree.insert("", tk.END, values=(
                    config_data.get("name", ""),
                    config_data.get("game_type", ""),
                    config_data.get("created_time", ""),
                    config_data.get("last_used", "")
                ))
                
    def on_config_select(self, event):
        """配置选择事件"""
        selection = self.config_tree.selection()
        if selection:
            item = self.config_tree.item(selection[0])
            config_name = item['values'][0]
            self.current_config = config_name
            if self.callback:
                self.callback(config_name)
                
    def load_configs(self):
        """加载配置"""
        try:
            if os.path.exists("data/configs"):
                for filename in os.listdir("data/configs"):
                    if filename.endswith(".json"):
                        config_path = os.path.join("data/configs", filename)
                        with open(config_path, "r", encoding="utf-8") as f:
                            config_data = json.load(f)
                            config_id = filename[:-5]  # 去掉.json后缀
                            self.configs[config_id] = config_data
                            
            self.filter_configs()
        except Exception as e:
            messagebox.showerror("错误", f"加载配置失败: {e}")
            
    def new_config(self):
        """新建配置"""
        self.notebook.select(1)  # 切换到编辑标签页
        # 清空编辑表单
        self.config_name_var.set("")
        self.config_game_var.set(self.get_game_display_name(self.current_game))
        self.config_desc_text.delete(1.0, tk.END)
        
    def edit_config(self, event=None):
        """编辑配置"""
        if not self.current_config:
            messagebox.showwarning("警告", "请先选择一个配置")
            return
            
        # 切换到编辑标签页并加载配置数据
        self.notebook.select(1)
        # 这里将加载选中配置的数据到编辑表单
        
    def save_config(self):
        """保存配置"""
        try:
            config_name = self.config_name_var.get().strip()
            if not config_name:
                messagebox.showwarning("警告", "请输入配置名称")
                return
                
            # 创建配置数据
            config_data = {
                "name": config_name,
                "game_type": self.config_game_var.get(),
                "description": self.config_desc_text.get(1.0, tk.END).strip(),
                "created_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "last_used": "",
                "regions": {}  # 区域数据将在框选功能完成后添加
            }
            
            # 保存到文件
            os.makedirs("data/configs", exist_ok=True)
            config_path = os.path.join("data/configs", f"{config_name}.json")
            with open(config_path, "w", encoding="utf-8") as f:
                json.dump(config_data, f, ensure_ascii=False, indent=2)
                
            # 更新内存中的配置
            self.configs[config_name] = config_data
            
            # 刷新列表
            self.filter_configs()
            
            messagebox.showinfo("成功", "配置保存成功")
            
        except Exception as e:
            messagebox.showerror("错误", f"保存配置失败: {e}")
            
    def cancel_edit(self):
        """取消编辑"""
        self.notebook.select(0)  # 切换回列表标签页
        
    def copy_config(self):
        """复制配置"""
        if not self.current_config:
            messagebox.showwarning("警告", "请先选择一个配置")
            return

        try:
            # 获取原配置数据
            original_config = self.configs[self.current_config]

            # 创建新配置名称
            new_name = f"{self.current_config}_副本"
            counter = 1
            while new_name in self.configs:
                new_name = f"{self.current_config}_副本{counter}"
                counter += 1

            # 复制配置数据
            new_config = original_config.copy()
            new_config["name"] = new_name
            new_config["created_time"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            new_config["last_used"] = ""

            # 保存新配置
            os.makedirs("data/configs", exist_ok=True)
            config_path = os.path.join("data/configs", f"{new_name}.json")
            with open(config_path, "w", encoding="utf-8") as f:
                json.dump(new_config, f, ensure_ascii=False, indent=2)

            # 更新内存中的配置
            self.configs[new_name] = new_config

            # 刷新列表
            self.filter_configs()

            messagebox.showinfo("成功", f"配置已复制为: {new_name}")

        except Exception as e:
            messagebox.showerror("错误", f"复制配置失败: {e}")

    def delete_config(self):
        """删除配置"""
        if not self.current_config:
            messagebox.showwarning("警告", "请先选择一个配置")
            return

        # 确认删除
        if messagebox.askyesno("确认", f"确定要删除配置 '{self.current_config}' 吗？"):
            try:
                # 删除文件
                config_path = os.path.join("data/configs", f"{self.current_config}.json")
                if os.path.exists(config_path):
                    os.remove(config_path)

                # 从内存中删除
                if self.current_config in self.configs:
                    del self.configs[self.current_config]

                # 清空当前选择
                self.current_config = ""

                # 刷新列表
                self.filter_configs()

                messagebox.showinfo("成功", "配置已删除")

            except Exception as e:
                messagebox.showerror("错误", f"删除配置失败: {e}")
        
    def refresh_configs(self):
        """刷新配置列表"""
        self.configs.clear()
        self.load_configs()
        
    def export_all_configs(self):
        """导出所有配置"""
        # 实现导出功能
        pass
        
    def export_selected_config(self):
        """导出选中配置"""
        # 实现导出功能
        pass
        
    def import_configs(self):
        """导入配置"""
        # 实现导入功能
        pass
        
    def import_from_clipboard(self):
        """从剪贴板导入"""
        # 实现剪贴板导入功能
        pass
        
    def create_backup(self):
        """创建备份"""
        # 实现备份功能
        pass
        
    def restore_backup(self):
        """恢复备份"""
        # 实现恢复功能
        pass

    def get_selected_config(self):
        """获取选中的配置"""
        selection = self.config_tree.selection()
        if selection:
            item = selection[0]
            config_id = self.config_tree.item(item, 'values')[0]  # 假设第一列是ID
            config_data = self.config_manager.get_config(config_id)
            if config_data:
                return {
                    'id': config_id,
                    'name': config_data.get('name', ''),
                    'game_type': config_data.get('game_type', ''),
                    'regions': config_data.get('regions', [])
                }
        return None
