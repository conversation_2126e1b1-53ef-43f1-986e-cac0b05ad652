#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
游戏选择器组件
"""

import tkinter as tk
from tkinter import ttk
from utils.dpi_helper import DPIAwareWidget

class GameSelector(DPIAwareWidget):
    """游戏选择器类"""
    
    # 支持的游戏类型
    GAME_TYPES = {
        "sudoku": "数独",
        "nonogram": "数织",
        "huarong": "华容道",
        "klotski": "滑块拼图",
        "custom": "自定义"
    }
    
    def __init__(self, parent, callback=None):
        super().__init__(parent)
        self.parent = parent
        self.callback = callback
        self.current_game = ""
        self.create_widgets()
        
    def create_widgets(self):
        """创建控件"""
        # 游戏类型选择
        game_label = ttk.Label(self.parent, text="选择游戏类型:")
        game_label.pack(anchor=tk.W, pady=(0, self.scaled(5)))
        
        # 游戏类型列表
        self.game_listbox = tk.Listbox(
            self.parent,
            height=8,
            font=('Microsoft YaHei UI', self.scaled(9)),
            selectmode=tk.SINGLE
        )
        self.game_listbox.pack(fill=tk.BOTH, expand=True, pady=(0, self.scaled(10)))
        
        # 添加游戏类型
        for game_id, game_name in self.GAME_TYPES.items():
            self.game_listbox.insert(tk.END, f"{game_name} ({game_id})")
            
        # 绑定选择事件
        self.game_listbox.bind('<<ListboxSelect>>', self.on_game_select)
        
        # 游戏描述
        desc_label = ttk.Label(self.parent, text="游戏描述:")
        desc_label.pack(anchor=tk.W, pady=(self.scaled(10), self.scaled(5)))
        
        self.desc_text = tk.Text(
            self.parent,
            height=6,
            width=25,
            font=('Microsoft YaHei UI', self.scaled(8)),
            wrap=tk.WORD,
            state=tk.DISABLED
        )
        self.desc_text.pack(fill=tk.X, pady=(0, self.scaled(10)))
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(self.desc_text)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.desc_text.config(yscrollcommand=scrollbar.set)
        scrollbar.config(command=self.desc_text.yview)
        
        # 操作按钮
        button_frame = ttk.Frame(self.parent)
        button_frame.pack(fill=tk.X, pady=(self.scaled(10), 0))
        
        self.select_btn = ttk.Button(
            button_frame,
            text="选择游戏",
            command=self.select_game,
            state=tk.DISABLED
        )
        self.select_btn.pack(fill=tk.X, pady=(0, self.scaled(5)))
        
        self.refresh_btn = ttk.Button(
            button_frame,
            text="刷新列表",
            command=self.refresh_games
        )
        self.refresh_btn.pack(fill=tk.X)
        
    def on_game_select(self, event):
        """游戏选择事件"""
        selection = self.game_listbox.curselection()
        if selection:
            # 获取选中的游戏
            selected_text = self.game_listbox.get(selection[0])
            game_id = selected_text.split('(')[1].split(')')[0]
            
            # 更新描述
            self.update_description(game_id)
            
            # 启用选择按钮
            self.select_btn.config(state=tk.NORMAL)
            
            self.current_game = game_id
            
    def update_description(self, game_id):
        """更新游戏描述"""
        descriptions = {
            "sudoku": "数独是一种逻辑推理的数字填充游戏。玩家需要根据9×9盘面上的已知数字，推理出所有剩余空格的数字，并满足每一行、每一列、每一个粗线宫内的数字均含1-9，不重复。",
            "nonogram": "数织（Nonogram）是一种逻辑拼图游戏，也称为绘图方块、像素拼图等。玩家需要根据每行和每列的数字提示，确定哪些方格需要填充，最终形成一幅图案。",
            "huarong": "华容道是中国古老的智力游戏，以三国故事为背景。玩家需要通过移动各个棋子，帮助曹操从初始位置移到棋盘最下方中央，从出口逃走。",
            "klotski": "滑块拼图是一种滑动拼图游戏。玩家需要通过滑动方块，将它们重新排列成目标状态。这类游戏考验玩家的空间思维和逻辑推理能力。",
            "custom": "自定义游戏类型，用户可以根据需要配置特定的识别区域和规则。适用于其他类型的网格游戏或特殊需求。"
        }
        
        desc = descriptions.get(game_id, "暂无描述")
        
        self.desc_text.config(state=tk.NORMAL)
        self.desc_text.delete(1.0, tk.END)
        self.desc_text.insert(1.0, desc)
        self.desc_text.config(state=tk.DISABLED)
        
    def select_game(self):
        """选择游戏"""
        if self.current_game and self.callback:
            self.callback(self.current_game)
            
    def refresh_games(self):
        """刷新游戏列表"""
        # 这里可以添加动态加载游戏类型的逻辑
        pass
        
    def set_game(self, game_id):
        """设置当前游戏"""
        for i in range(self.game_listbox.size()):
            item_text = self.game_listbox.get(i)
            if f"({game_id})" in item_text:
                self.game_listbox.selection_clear(0, tk.END)
                self.game_listbox.selection_set(i)
                self.game_listbox.see(i)
                self.current_game = game_id
                self.update_description(game_id)
                self.select_btn.config(state=tk.NORMAL)
                break

    def get_selected_game(self):
        """获取选中的游戏类型"""
        return self.current_game

    def get_selected_game_info(self):
        """获取选中游戏的详细信息"""
        if self.current_game and self.current_game in GAME_TYPES:
            return {
                'type': self.current_game,
                'name': GAME_TYPES[self.current_game],
                'description': GAME_DESCRIPTIONS.get(self.current_game, '')
            }
        return None
