#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
区域选择器组件
"""

import tkinter as tk
from tkinter import ttk, messagebox
from utils.dpi_helper import DPIAwareWidget
from core.screen_selector import ScreenSelector
from core.config_manager import Config<PERSON>anager
from core.recognition_manager import RecognitionManager

class RegionSelector(DPIAwareWidget):
    """区域选择器类"""
    
    def __init__(self, parent, callback=None):
        super().__init__(parent)
        self.parent = parent
        self.callback = callback
        self.current_game = ""
        self.selected_regions = []
        self.config_manager = ConfigManager()
        self.recognition_manager = RecognitionManager()
        self.screen_selector = None
        self.create_widgets()
        
    def create_widgets(self):
        """创建控件"""
        # 游戏类型显示
        game_frame = ttk.Frame(self.parent)
        game_frame.pack(fill=tk.X, pady=(0, self.scaled(10)))
        
        ttk.Label(game_frame, text="当前游戏:").pack(side=tk.LEFT)
        self.game_label = ttk.Label(game_frame, text="未选择", foreground="gray")
        self.game_label.pack(side=tk.LEFT, padx=(self.scaled(5), 0))
        
        # 框选模式选择
        mode_frame = ttk.LabelFrame(self.parent, text="框选模式", padding=self.scaled_tuple(5, 5))
        mode_frame.pack(fill=tk.X, pady=(0, self.scaled(10)))

        self.mode_var = tk.StringVar(value="single")
        ttk.Radiobutton(mode_frame, text="单区域", variable=self.mode_var, value="single", command=self.on_mode_change).pack(anchor=tk.W)
        ttk.Radiobutton(mode_frame, text="多区域", variable=self.mode_var, value="multi", command=self.on_mode_change).pack(anchor=tk.W)
        ttk.Radiobutton(mode_frame, text="网格模式", variable=self.mode_var, value="grid", command=self.on_mode_change).pack(anchor=tk.W)

        # 网格设置（仅在网格模式下显示）
        self.grid_frame = ttk.Frame(mode_frame)

        grid_size_frame = ttk.Frame(self.grid_frame)
        grid_size_frame.pack(fill=tk.X, pady=(self.scaled(5), 0))

        ttk.Label(grid_size_frame, text="网格比例:").pack(side=tk.LEFT)

        self.grid_rows_var = tk.StringVar(value="9")
        self.grid_cols_var = tk.StringVar(value="9")

        ttk.Entry(grid_size_frame, textvariable=self.grid_rows_var, width=4).pack(side=tk.LEFT, padx=(self.scaled(5), 0))
        ttk.Label(grid_size_frame, text="×").pack(side=tk.LEFT, padx=(self.scaled(2), self.scaled(2)))
        ttk.Entry(grid_size_frame, textvariable=self.grid_cols_var, width=4).pack(side=tk.LEFT)
        ttk.Label(grid_size_frame, text="(横×竖)").pack(side=tk.LEFT, padx=(self.scaled(5), 0))

        # 自动回正和智能翻转说明
        auto_features_frame = ttk.Frame(self.grid_frame)
        auto_features_frame.pack(fill=tk.X, pady=(self.scaled(2), 0))

        ttk.Label(auto_features_frame, text="✓ 自动回正为正方形", foreground="green", font=('Microsoft YaHei UI', self.scaled(8))).pack(side=tk.LEFT)
        ttk.Label(auto_features_frame, text="✓ 智能翻转适配", foreground="blue", font=('Microsoft YaHei UI', self.scaled(8))).pack(side=tk.LEFT, padx=(self.scaled(10), 0))
        
        # 区域列表
        list_frame = ttk.LabelFrame(self.parent, text="已选区域", padding=self.scaled_tuple(5, 5))
        list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, self.scaled(10)))
        
        # 创建区域列表
        self.region_listbox = tk.Listbox(
            list_frame,
            height=8,
            font=('Microsoft YaHei UI', self.scaled(8))
        )
        self.region_listbox.pack(fill=tk.BOTH, expand=True, pady=(0, self.scaled(5)))
        
        # 区域信息显示
        info_frame = ttk.Frame(list_frame)
        info_frame.pack(fill=tk.X)

        self.region_count_label = ttk.Label(info_frame, text="区域数量: 0")
        self.region_count_label.pack(side=tk.LEFT)

        # 区域管理按钮
        ttk.Button(info_frame, text="删除选中", command=self.delete_selected_region).pack(side=tk.RIGHT, padx=(self.scaled(5), 0))
        ttk.Button(info_frame, text="删除网格区域", command=self.delete_grid_zone).pack(side=tk.RIGHT)
        
        # 操作按钮
        button_frame = ttk.Frame(self.parent)
        button_frame.pack(fill=tk.X, pady=(self.scaled(10), 0))
        
        # 第一行按钮
        button_row1 = ttk.Frame(button_frame)
        button_row1.pack(fill=tk.X, pady=(0, self.scaled(5)))
        
        self.start_select_btn = ttk.Button(
            button_row1,
            text="开始框选",
            command=self.start_selection,
            state=tk.DISABLED
        )
        self.start_select_btn.pack(fill=tk.X)
        
        # 第二行按钮
        button_row2 = ttk.Frame(button_frame)
        button_row2.pack(fill=tk.X, pady=(0, self.scaled(5)))
        
        ttk.Button(button_row2, text="预览区域", command=self.preview_regions).pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, self.scaled(2)))
        ttk.Button(button_row2, text="清空选择", command=self.clear_regions).pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=(self.scaled(2), 0))
        
        # 第三行按钮
        button_row3 = ttk.Frame(button_frame)
        button_row3.pack(fill=tk.X, pady=(0, self.scaled(5)))

        ttk.Button(button_row3, text="保存区域", command=self.save_regions).pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, self.scaled(2)))
        ttk.Button(button_row3, text="加载区域", command=self.load_regions).pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=(self.scaled(2), 0))

        # 第四行按钮 - 识别测试
        button_row4 = ttk.Frame(button_frame)
        button_row4.pack(fill=tk.X, pady=(0, self.scaled(5)))

        ttk.Button(button_row4, text="测试识别", command=self.test_recognition).pack(fill=tk.X)
        
        # 高级选项
        advanced_frame = ttk.LabelFrame(self.parent, text="高级选项", padding=self.scaled_tuple(5, 5))
        advanced_frame.pack(fill=tk.X, pady=(self.scaled(10), 0))
        
        # 自动检测选项
        self.auto_detect_var = tk.BooleanVar()
        ttk.Checkbutton(advanced_frame, text="自动检测网格", variable=self.auto_detect_var).pack(anchor=tk.W)
        
        # 精确模式选项
        self.precise_mode_var = tk.BooleanVar()
        ttk.Checkbutton(advanced_frame, text="精确模式", variable=self.precise_mode_var).pack(anchor=tk.W)
        
        # 相对坐标选项
        self.relative_coord_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(advanced_frame, text="使用相对坐标", variable=self.relative_coord_var).pack(anchor=tk.W)
        
    def set_game_type(self, game_type):
        """设置游戏类型"""
        self.current_game = game_type
        
        # 更新游戏显示
        game_names = {
            "sudoku": "数独",
            "nonogram": "数织",
            "huarong": "华容道", 
            "klotski": "滑块拼图",
            "custom": "自定义"
        }
        
        display_name = game_names.get(game_type, game_type)
        self.game_label.config(text=display_name, foreground="black")
        
        # 启用开始框选按钮
        self.start_select_btn.config(state=tk.NORMAL)
        
        # 根据游戏类型设置默认模式和网格大小
        if game_type == "nonogram":
            self.mode_var.set("multi")  # 数织通常需要多区域
            self.grid_rows_var.set("15")
            self.grid_cols_var.set("15")
        elif game_type == "sudoku":
            self.mode_var.set("grid")  # 数独推荐网格模式
            self.grid_rows_var.set("9")
            self.grid_cols_var.set("9")
        elif game_type in ["huarong", "klotski"]:
            self.mode_var.set("single")  # 其他游戏通常单区域
            self.grid_rows_var.set("4")
            self.grid_cols_var.set("4")
        else:
            self.mode_var.set("single")
            self.grid_rows_var.set("10")
            self.grid_cols_var.set("10")

        # 更新网格设置显示
        self.on_mode_change()

    def on_mode_change(self):
        """模式变化处理"""
        mode = self.mode_var.get()
        if mode == "grid":
            self.grid_frame.pack(fill=tk.X, pady=(self.scaled(5), 0))
        else:
            self.grid_frame.pack_forget()

    def start_selection(self):
        """开始区域选择"""
        if not self.current_game:
            messagebox.showwarning("警告", "请先选择游戏类型")
            return

        mode = self.mode_var.get()
        auto_detect = self.auto_detect_var.get()
        precise_mode = self.precise_mode_var.get()
        relative_coord = self.relative_coord_var.get()

        try:
            # 创建屏幕选择器
            self.screen_selector = ScreenSelector(
                callback=self.on_regions_selected,
                mode=mode
            )

            # 设置游戏类型（用于网格检测）
            if self.current_game:
                self.screen_selector.set_game_type(self.current_game)

            # 设置网格大小（如果是网格模式）
            if mode == "grid":
                try:
                    rows = int(self.grid_rows_var.get())
                    cols = int(self.grid_cols_var.get())
                    self.screen_selector.set_grid_size(rows, cols)
                except ValueError:
                    messagebox.showerror("错误", "网格大小必须是数字")
                    return

            # 开始选择
            self.screen_selector.start_selection(
                mode=mode,
                auto_detect=auto_detect,
                precise_mode=precise_mode
            )

        except Exception as e:
            messagebox.showerror("错误", f"启动区域选择失败: {e}")

    def on_regions_selected(self, regions):
        """区域选择完成回调"""
        if regions:
            # 清空之前的选择
            self.selected_regions.clear()

            # 添加新选择的区域
            for region in regions:
                self.add_region(
                    region.get('name', f'区域{len(self.selected_regions) + 1}'),
                    (region['x'], region['y'], region['width'], region['height'])
                )

            messagebox.showinfo("完成", f"成功选择了 {len(regions)} 个区域")
        else:
            messagebox.showinfo("取消", "区域选择已取消")
        
    def add_region(self, name, coords):
        """添加区域"""
        region_info = {
            "name": name,
            "coords": coords,  # (x, y, width, height)
            "game_type": self.current_game
        }
        
        self.selected_regions.append(region_info)
        
        # 更新列表显示
        self.update_region_list()
        
        # 回调通知
        if self.callback:
            self.callback(f"{name}: {coords}")
            
    def update_region_list(self):
        """更新区域列表显示"""
        # 清空列表
        self.region_listbox.delete(0, tk.END)
        
        # 添加区域
        for i, region in enumerate(self.selected_regions):
            coords = region["coords"]
            display_text = f"{i+1}. {region['name']} ({coords[0]}, {coords[1]}, {coords[2]}x{coords[3]})"
            self.region_listbox.insert(tk.END, display_text)
            
        # 更新计数
        self.region_count_label.config(text=f"区域数量: {len(self.selected_regions)}")
        
    def preview_regions(self):
        """预览区域"""
        if not self.selected_regions:
            messagebox.showwarning("警告", "没有选择的区域")
            return
            
        # 这里将实现区域预览功能
        messagebox.showinfo("预览", f"将预览 {len(self.selected_regions)} 个区域")
        
    def clear_regions(self):
        """清空区域选择"""
        if self.selected_regions:
            if messagebox.askyesno("确认", "确定要清空所有选择的区域吗？"):
                self.selected_regions.clear()
                self.update_region_list()
        
    def save_regions(self):
        """保存区域配置"""
        if not self.selected_regions:
            messagebox.showwarning("警告", "没有选择的区域")
            return

        if not self.current_game:
            messagebox.showwarning("警告", "请先选择游戏类型")
            return

        # 弹出保存对话框
        self.show_save_dialog()

    def show_save_dialog(self):
        """显示保存对话框"""
        dialog = tk.Toplevel(self.parent)
        dialog.title("保存区域配置")
        dialog.geometry(f"{self.scaled(400)}x{self.scaled(300)}")
        dialog.transient(self.parent)
        dialog.grab_set()

        # 配置名称
        ttk.Label(dialog, text="配置名称:").pack(pady=self.scaled(10))
        name_var = tk.StringVar()
        name_entry = ttk.Entry(dialog, textvariable=name_var, width=40)
        name_entry.pack(pady=self.scaled(5))
        name_entry.focus()

        # 描述
        ttk.Label(dialog, text="描述:").pack(pady=(self.scaled(10), self.scaled(5)))
        desc_text = tk.Text(dialog, width=40, height=5)
        desc_text.pack(pady=self.scaled(5))

        # 按钮
        button_frame = ttk.Frame(dialog)
        button_frame.pack(pady=self.scaled(20))

        def save_config():
            name = name_var.get().strip()
            if not name:
                messagebox.showwarning("警告", "请输入配置名称")
                return

            try:
                # 准备区域数据
                regions_data = []
                for region in self.selected_regions:
                    regions_data.append({
                        'name': region['name'],
                        'x': region['coords'][0],
                        'y': region['coords'][1],
                        'width': region['coords'][2],
                        'height': region['coords'][3],
                        'type': 'manual'
                    })

                # 创建配置
                config_id = self.config_manager.create_config(
                    name=name,
                    game_type=self.current_game,
                    description=desc_text.get(1.0, tk.END).strip(),
                    regions=regions_data
                )

                messagebox.showinfo("成功", f"配置已保存: {name}")
                dialog.destroy()

            except Exception as e:
                messagebox.showerror("错误", f"保存配置失败: {e}")

        def cancel_save():
            dialog.destroy()

        ttk.Button(button_frame, text="保存", command=save_config).pack(side=tk.LEFT, padx=self.scaled(5))
        ttk.Button(button_frame, text="取消", command=cancel_save).pack(side=tk.LEFT, padx=self.scaled(5))

    def load_regions(self):
        """加载区域配置"""
        # 获取当前游戏类型的配置
        configs = self.config_manager.get_configs_by_game_type(self.current_game)

        if not configs:
            messagebox.showinfo("提示", f"没有找到 {self.current_game} 类型的配置")
            return

        # 显示配置选择对话框
        self.show_load_dialog(configs)

    def show_load_dialog(self, configs):
        """显示加载对话框"""
        dialog = tk.Toplevel(self.parent)
        dialog.title("加载区域配置")
        dialog.geometry(f"{self.scaled(500)}x{self.scaled(400)}")
        dialog.transient(self.parent)
        dialog.grab_set()

        # 配置列表
        ttk.Label(dialog, text="选择配置:").pack(pady=self.scaled(10))

        # 创建列表框
        listbox_frame = ttk.Frame(dialog)
        listbox_frame.pack(fill=tk.BOTH, expand=True, padx=self.scaled(20), pady=self.scaled(10))

        config_listbox = tk.Listbox(listbox_frame, height=10)
        config_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        scrollbar = ttk.Scrollbar(listbox_frame, orient=tk.VERTICAL, command=config_listbox.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        config_listbox.config(yscrollcommand=scrollbar.set)

        # 填充配置列表
        config_list = list(configs.items())
        for config_id, config_data in config_list:
            name = config_data.get('name', '未命名')
            region_count = len(config_data.get('regions', []))
            created_time = config_data.get('created_time', '')[:10]  # 只显示日期
            display_text = f"{name} ({region_count}个区域) - {created_time}"
            config_listbox.insert(tk.END, display_text)

        # 按钮
        button_frame = ttk.Frame(dialog)
        button_frame.pack(pady=self.scaled(20))

        def load_config():
            selection = config_listbox.curselection()
            if not selection:
                messagebox.showwarning("警告", "请选择一个配置")
                return

            try:
                # 获取选中的配置
                config_id, config_data = config_list[selection[0]]

                # 清空当前选择
                self.selected_regions.clear()

                # 加载区域数据
                regions = config_data.get('regions', [])
                for region_data in regions:
                    self.add_region(
                        region_data.get('name', '区域'),
                        (region_data['x'], region_data['y'], region_data['width'], region_data['height'])
                    )

                # 更新最后使用时间
                self.config_manager.update_last_used(config_id)

                messagebox.showinfo("成功", f"已加载配置: {config_data.get('name', '未命名')}")
                dialog.destroy()

            except Exception as e:
                messagebox.showerror("错误", f"加载配置失败: {e}")

        def cancel_load():
            dialog.destroy()

        ttk.Button(button_frame, text="加载", command=load_config).pack(side=tk.LEFT, padx=self.scaled(5))
        ttk.Button(button_frame, text="取消", command=cancel_load).pack(side=tk.LEFT, padx=self.scaled(5))
        
    def get_selected_regions(self):
        """获取选择的区域"""
        return self.selected_regions.copy()
        
    def set_regions(self, regions):
        """设置区域列表"""
        self.selected_regions = regions.copy()
        self.update_region_list()

    def test_recognition(self):
        """测试识别功能"""
        if not self.selected_regions:
            messagebox.showwarning("警告", "请先选择区域")
            return

        if not self.current_game:
            messagebox.showwarning("警告", "请先选择游戏类型")
            return

        try:
            # 创建临时配置用于测试
            temp_regions = []
            for region in self.selected_regions:
                temp_regions.append({
                    'name': region['name'],
                    'x': region['coords'][0],
                    'y': region['coords'][1],
                    'width': region['coords'][2],
                    'height': region['coords'][3],
                    'type': 'manual'
                })

            # 创建临时配置
            temp_config_id = self.config_manager.create_config(
                name=f"临时测试_{self.current_game}",
                game_type=self.current_game,
                description="临时测试配置",
                regions=temp_regions
            )

            # 执行识别
            result = self.recognition_manager.recognize_game(temp_config_id)

            # 显示结果
            self.show_recognition_result(result)

            # 删除临时配置
            self.config_manager.delete_config(temp_config_id)

        except Exception as e:
            messagebox.showerror("错误", f"识别测试失败: {e}")

    def show_recognition_result(self, result):
        """显示识别结果"""
        # 创建结果显示窗口
        result_window = tk.Toplevel(self.parent)
        result_window.title("识别结果")
        result_window.geometry(f"{self.scaled(600)}x{self.scaled(500)}")
        result_window.transient(self.parent)
        result_window.grab_set()

        # 创建文本框显示结果
        text_frame = ttk.Frame(result_window)
        text_frame.pack(fill=tk.BOTH, expand=True, padx=self.scaled(10), pady=self.scaled(10))

        result_text = tk.Text(text_frame, wrap=tk.WORD, font=('Consolas', self.scaled(9)))
        scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=result_text.yview)
        result_text.config(yscrollcommand=scrollbar.set)

        result_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 格式化并显示结果
        formatted_result = self.format_recognition_result(result)
        result_text.insert(tk.END, formatted_result)
        result_text.config(state=tk.DISABLED)

        # 关闭按钮
        button_frame = ttk.Frame(result_window)
        button_frame.pack(fill=tk.X, padx=self.scaled(10), pady=(0, self.scaled(10)))

        ttk.Button(button_frame, text="关闭", command=result_window.destroy).pack(side=tk.RIGHT)

    def format_recognition_result(self, result):
        """格式化识别结果"""
        import json

        if 'error' in result:
            return f"识别失败: {result['error']}"

        game_type = result.get('game_type', '未知')
        formatted = f"游戏类型: {game_type}\n"
        formatted += "=" * 50 + "\n\n"

        if game_type == 'sudoku':
            if 'grid' in result:
                formatted += "数独网格:\n"
                grid = result['grid']
                for row in grid:
                    formatted += " ".join(str(cell) if cell != 0 else '.' for cell in row) + "\n"
                formatted += "\n"

            if 'stats' in result:
                stats = result['stats']
                formatted += f"识别统计:\n"
                formatted += f"  总单元格: {stats.get('total_cells', 0)}\n"
                formatted += f"  识别数字: {stats.get('recognized_digits', 0)}\n"
                formatted += f"  空白单元格: {stats.get('empty_cells', 0)}\n"
                formatted += f"  平均置信度: {stats.get('average_confidence', 0):.1f}%\n"
                formatted += f"  识别率: {stats.get('recognition_rate', 0):.1f}%\n"

        elif game_type == 'nonogram':
            if 'row_hints' in result:
                formatted += "横提示:\n"
                for i, hints in enumerate(result['row_hints']):
                    formatted += f"  第{i+1}行: {hints}\n"
                formatted += "\n"

            if 'col_hints' in result:
                formatted += "竖提示:\n"
                for i, hints in enumerate(result['col_hints']):
                    formatted += f"  第{i+1}列: {hints}\n"
                formatted += "\n"

            if 'main_grid' in result:
                formatted += "主网格:\n"
                grid = result['main_grid']
                for row in grid:
                    formatted += " ".join('■' if cell else '□' for cell in row) + "\n"

        else:
            # 其他游戏类型，显示原始JSON
            formatted += "识别结果:\n"
            formatted += json.dumps(result, ensure_ascii=False, indent=2)

        return formatted

    def delete_selected_region(self):
        """删除选中的区域"""
        selection = self.region_listbox.curselection()
        if not selection:
            messagebox.showwarning("警告", "请先选择要删除的区域")
            return

        selected_index = selection[0]

        # 删除选中的区域
        if selected_index < len(self.selected_regions):
            region_name = self.selected_regions[selected_index]['name']
            del self.selected_regions[selected_index]
            self.update_region_list()
            messagebox.showinfo("成功", f"已删除区域: {region_name}")

    def delete_grid_zone(self):
        """删除网格区域（弹出选择对话框）"""
        if not hasattr(self.screen_selector, 'grid_zones') or not self.screen_selector.grid_zones:
            messagebox.showinfo("提示", "没有网格区域可删除")
            return

        # 创建选择对话框
        dialog = tk.Toplevel(self.parent)
        dialog.title("选择要删除的网格区域")
        dialog.geometry("300x200")
        dialog.transient(self.parent)
        dialog.grab_set()

        # 区域列表
        listbox = tk.Listbox(dialog)
        listbox.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        for zone in self.screen_selector.grid_zones:
            listbox.insert(tk.END, f"{zone['name']} - {zone['grid_size']}")

        # 按钮
        button_frame = ttk.Frame(dialog)
        button_frame.pack(fill=tk.X, padx=10, pady=(0, 10))

        def delete_selected():
            selection = listbox.curselection()
            if selection:
                zone_name = self.screen_selector.grid_zones[selection[0]]['name']
                self.delete_grid_zone_by_name(zone_name)
                dialog.destroy()
            else:
                messagebox.showwarning("警告", "请选择要删除的区域")

        ttk.Button(button_frame, text="删除", command=delete_selected).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="取消", command=dialog.destroy).pack(side=tk.LEFT)

    def delete_grid_zone_by_name(self, zone_name):
        """根据名称删除网格区域"""
        if hasattr(self.screen_selector, 'clear_grid_zone'):
            self.screen_selector.clear_grid_zone(zone_name)

        # 从选择区域中删除该区域的所有网格
        original_count = len(self.selected_regions)
        self.selected_regions = [
            region for region in self.selected_regions
            if region.get('zone_name') != zone_name
        ]

        deleted_count = original_count - len(self.selected_regions)
        self.update_region_list()
        messagebox.showinfo("成功", f"已删除网格区域 {zone_name}，共删除 {deleted_count} 个网格单元")
