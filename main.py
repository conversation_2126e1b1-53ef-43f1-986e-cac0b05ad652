#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
游戏辅助工具主程序
支持多种网格类游戏的识别和辅助
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# 导入自定义模块
from gui.main_window import MainWindow
from utils.dpi_helper import setup_dpi_awareness
from utils.admin_manager import AdminManager, MuMuWindowManager

def check_admin_and_setup():
    """检查管理员权限并设置MuMu窗口"""
    admin_manager = AdminManager()

    if not admin_manager.is_admin:
        # 询问是否需要管理员权限
        result = messagebox.askyesnocancel(
            "管理员权限",
            "检测到程序未以管理员身份运行。\n\n"
            "要控制MuMu模拟器窗口需要管理员权限。\n\n"
            "是否重新以管理员身份启动程序？\n\n"
            "选择'是'：重新启动并获取管理员权限\n"
            "选择'否'：继续运行但功能受限\n"
            "选择'取消'：退出程序"
        )

        if result is True:
            # 用户选择获取管理员权限
            if admin_manager.request_admin_privileges():
                return None  # 程序将重新启动
            else:
                messagebox.showerror("错误", "获取管理员权限失败")
                return False
        elif result is False:
            # 用户选择继续运行
            messagebox.showwarning(
                "功能受限",
                "程序将在受限模式下运行。\n\n"
                "某些窗口控制功能可能无法正常工作。"
            )
            return True
        else:
            # 用户选择取消
            return False
    else:
        # 已有管理员权限，尝试设置MuMu窗口
        mumu_manager = MuMuWindowManager()

        # 查找MuMu窗口
        if mumu_manager.find_and_select_mumu_window():
            # 询问是否设置MuMu窗口
            result = messagebox.askyesno(
                "MuMu窗口设置",
                f"找到MuMu窗口: {mumu_manager.current_mumu_window['title']}\n\n"
                "是否将其置顶并带到前台？"
            )

            if result:
                if mumu_manager.setup_mumu_window():
                    messagebox.showinfo("成功", "MuMu窗口设置完成")
                else:
                    messagebox.showwarning("警告", "MuMu窗口设置失败")
        else:
            messagebox.showinfo(
                "提示",
                "未找到MuMu模拟器窗口。\n\n"
                "请确保MuMu模拟器正在运行，\n"
                "稍后可在程序中手动设置。"
            )

        return True

def main():
    """主程序入口"""
    try:
        # 设置DPI感知
        setup_dpi_awareness()

        # 检查管理员权限并设置MuMu窗口
        setup_result = check_admin_and_setup()

        if setup_result is None:
            # 程序正在重新启动
            return
        elif setup_result is False:
            # 用户取消或设置失败
            sys.exit(0)

        # 创建主窗口
        root = tk.Tk()
        app = MainWindow(root)

        # 设置窗口关闭事件
        def on_closing():
            try:
                # 通过主窗口清理MuMu窗口设置
                if hasattr(app, 'mumu_manager') and app.mumu_manager.current_mumu_window:
                    print("正在清理MuMu窗口设置...")
                    if app.mumu_manager.cleanup_mumu_window():
                        print("✓ MuMu窗口置顶已取消")
                    else:
                        print("✗ 取消MuMu窗口置顶失败")

                # 调用主窗口的清理方法
                app.on_closing()

            except Exception as e:
                print(f"清理时出错: {e}")
                root.destroy()

        root.protocol("WM_DELETE_WINDOW", on_closing)

        # 启动主循环
        root.mainloop()

    except Exception as e:
        messagebox.showerror("错误", f"程序启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
