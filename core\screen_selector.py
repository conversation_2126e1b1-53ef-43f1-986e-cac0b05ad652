#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专业屏幕区域选择器
支持多种选择模式和精确定位
"""

import tkinter as tk
from tkinter import ttk, messagebox
import pyautogui
import cv2
import numpy as np
from PIL import Image, ImageTk
import threading
import time

class ScreenSelector:
    """屏幕区域选择器"""
    
    def __init__(self, callback=None, mode="single"):
        self.callback = callback
        self.mode = mode  # single, multi, grid
        self.selected_regions = []
        self.current_selection = None
        self.is_selecting = False
        self.overlay_window = None
        self.canvas = None
        self.screenshot = None
        self.grid_zones = []  # 存储多个网格区域
        self.current_zone_name = "分区A"  # 当前网格区域名称
        self.grid_rows = 9  # 默认网格行数
        self.grid_cols = 9  # 默认网格列数
        
    def start_selection(self, mode="single", auto_detect=False, precise_mode=False):
        """开始区域选择"""
        self.mode = mode
        self.auto_detect = auto_detect
        self.precise_mode = precise_mode
        
        try:
            # 截取全屏
            self.take_screenshot()
            
            # 创建覆盖窗口
            self.create_overlay_window()
            
            # 开始选择
            self.is_selecting = True
            
        except Exception as e:
            messagebox.showerror("错误", f"启动区域选择失败: {e}")
            
    def take_screenshot(self):
        """截取屏幕截图"""
        try:
            # 获取屏幕尺寸
            screen_width, screen_height = pyautogui.size()
            
            # 截取全屏
            screenshot = pyautogui.screenshot()
            
            # 转换为OpenCV格式
            self.screenshot = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
            
            # 保存PIL格式用于显示
            self.pil_screenshot = screenshot
            
        except Exception as e:
            raise Exception(f"截图失败: {e}")
            
    def create_overlay_window(self):
        """创建覆盖窗口"""
        try:
            # 创建全屏覆盖窗口
            self.overlay_window = tk.Toplevel()
            self.overlay_window.title("区域选择")
            
            # 设置全屏
            self.overlay_window.attributes('-fullscreen', True)
            self.overlay_window.attributes('-topmost', True)
            self.overlay_window.attributes('-alpha', 0.3)  # 半透明
            
            # 创建画布
            self.canvas = tk.Canvas(
                self.overlay_window,
                highlightthickness=0,
                cursor="crosshair"
            )
            self.canvas.pack(fill=tk.BOTH, expand=True)
            
            # 显示截图作为背景
            self.display_screenshot()
            
            # 绑定事件
            self.bind_events()
            
            # 创建信息面板
            self.create_info_panel()
            
        except Exception as e:
            raise Exception(f"创建覆盖窗口失败: {e}")
            
    def display_screenshot(self):
        """显示截图背景"""
        try:
            # 获取屏幕尺寸
            screen_width = self.overlay_window.winfo_screenwidth()
            screen_height = self.overlay_window.winfo_screenheight()
            
            # 调整截图尺寸
            resized_image = self.pil_screenshot.resize((screen_width, screen_height), Image.Resampling.LANCZOS)
            
            # 转换为Tkinter格式
            self.tk_image = ImageTk.PhotoImage(resized_image)
            
            # 在画布上显示
            self.canvas.create_image(0, 0, anchor=tk.NW, image=self.tk_image)
            
        except Exception as e:
            print(f"显示截图失败: {e}")
            
    def create_info_panel(self):
        """创建信息面板"""
        # 信息面板框架
        info_frame = tk.Frame(self.overlay_window, bg='black', relief=tk.RAISED, bd=2)
        info_frame.place(x=10, y=10)
        
        # 模式信息
        mode_text = {"single": "单区域", "multi": "多区域", "grid": "网格模式"}
        tk.Label(info_frame, text=f"模式: {mode_text.get(self.mode, self.mode)}", 
                fg='white', bg='black', font=('Arial', 10)).pack(padx=5, pady=2)
        
        # 坐标信息
        self.coord_label = tk.Label(info_frame, text="坐标: (0, 0)", 
                                   fg='white', bg='black', font=('Arial', 10))
        self.coord_label.pack(padx=5, pady=2)
        
        # 尺寸信息
        self.size_label = tk.Label(info_frame, text="尺寸: 0x0", 
                                  fg='white', bg='black', font=('Arial', 10))
        self.size_label.pack(padx=5, pady=2)
        
        # 区域计数
        self.count_label = tk.Label(info_frame, text=f"已选: {len(self.selected_regions)}", 
                                   fg='white', bg='black', font=('Arial', 10))
        self.count_label.pack(padx=5, pady=2)
        
        # 操作提示
        tk.Label(info_frame, text="左键拖拽选择 | 右键完成 | ESC取消", 
                fg='yellow', bg='black', font=('Arial', 9)).pack(padx=5, pady=2)
                
    def bind_events(self):
        """绑定事件"""
        # 鼠标事件
        self.canvas.bind('<Button-1>', self.on_mouse_press)
        self.canvas.bind('<B1-Motion>', self.on_mouse_drag)
        self.canvas.bind('<ButtonRelease-1>', self.on_mouse_release)
        self.canvas.bind('<Button-3>', self.on_right_click)
        self.canvas.bind('<Motion>', self.on_mouse_move)
        
        # 键盘事件
        self.overlay_window.bind('<Escape>', self.on_escape)
        self.overlay_window.bind('<Return>', self.on_enter)
        self.overlay_window.bind('<Delete>', self.on_delete)
        
        # 焦点
        self.overlay_window.focus_set()
        
    def on_mouse_press(self, event):
        """鼠标按下事件"""
        self.start_x = event.x
        self.start_y = event.y
        self.current_selection = None
        
    def on_mouse_drag(self, event):
        """鼠标拖拽事件"""
        if hasattr(self, 'start_x') and hasattr(self, 'start_y'):
            # 删除之前的选择框
            if self.current_selection:
                self.canvas.delete(self.current_selection)
                
            # 计算选择区域
            x1, y1 = self.start_x, self.start_y
            x2, y2 = event.x, event.y
            
            # 确保坐标正确
            left = min(x1, x2)
            top = min(y1, y2)
            right = max(x1, x2)
            bottom = max(y1, y2)
            
            # 绘制选择框
            self.current_selection = self.canvas.create_rectangle(
                left, top, right, bottom,
                outline='red', width=2, fill='', stipple='gray50'
            )
            
            # 更新尺寸信息
            width = right - left
            height = bottom - top
            self.size_label.config(text=f"尺寸: {width}x{height}")
            
    def on_mouse_release(self, event):
        """鼠标释放事件"""
        if hasattr(self, 'start_x') and hasattr(self, 'start_y'):
            # 计算最终选择区域
            x1, y1 = self.start_x, self.start_y
            x2, y2 = event.x, event.y
            
            # 确保坐标正确
            left = min(x1, x2)
            top = min(y1, y2)
            right = max(x1, x2)
            bottom = max(y1, y2)
            
            width = right - left
            height = bottom - top
            
            # 检查选择区域是否有效
            if width > 10 and height > 10:
                if self.mode == "grid":
                    # 网格模式：自动检测网格并分割
                    grid_regions = self.detect_grid_regions(left, top, right, bottom)

                    # 创建网格区域信息
                    actual_rows = len(set(r['grid_row'] for r in grid_regions))
                    actual_cols = len(set(r['grid_col'] for r in grid_regions))

                    grid_zone = {
                        'name': self.current_zone_name,
                        'regions': grid_regions,
                        'original_bounds': {'x': left, 'y': top, 'width': right-left, 'height': bottom-top},
                        'actual_bounds': {'x': int(grid_regions[0]['x']), 'y': int(grid_regions[0]['y']),
                                        'width': int(actual_cols * grid_regions[0]['width']),
                                        'height': int(actual_rows * grid_regions[0]['height'])},
                        'original_grid_size': f"{self.grid_rows}×{self.grid_cols}",
                        'actual_grid_size': f"{actual_rows}×{actual_cols}",
                        'cell_size': int(grid_regions[0]['width']),
                        'is_square': True,
                        'flipped': getattr(self, 'last_grid_flipped', False)
                    }

                    self.grid_zones.append(grid_zone)
                    self.selected_regions.extend(grid_regions)

                    # 绘制网格
                    self.draw_grid_regions(grid_regions, self.current_zone_name)

                    # 更新计数和区域名称
                    zone_count = len(self.grid_zones)
                    self.count_label.config(text=f"已选: {len(self.selected_regions)} ({zone_count}个网格区域)")

                    # 准备下一个区域名称
                    next_zone = chr(ord('A') + zone_count)  # A, B, C, D...
                    self.current_zone_name = f"分区{next_zone}"

                    # 显示回正和翻转信息
                    original_bounds = grid_zone['original_bounds']
                    actual_bounds = grid_zone['actual_bounds']
                    cell_size = grid_zone['cell_size']
                    flipped = grid_zone['flipped']
                    original_grid = grid_zone['original_grid_size']
                    actual_grid = grid_zone['actual_grid_size']

                    flip_text = " (智能翻转)" if flipped else ""
                    info_text = (f"✓ {self.current_zone_name}已创建: {original_grid}→{actual_grid}网格{flip_text}\n"
                               f"原始区域: {original_bounds['width']}×{original_bounds['height']}px\n"
                               f"回正为: {actual_bounds['width']}×{actual_bounds['height']}px (正方形{cell_size}×{cell_size}px)")

                    # 打印回正和翻转信息
                    print(f"{info_text}")
                    print(f"继续选择{self.current_zone_name}或右键完成")
                else:
                    # 单区域或多区域模式
                    region = {
                        'x': left,
                        'y': top,
                        'width': width,
                        'height': height,
                        'name': f'区域{len(self.selected_regions) + 1}'
                    }

                    # 添加到选择列表
                    self.selected_regions.append(region)

                    # 更新计数
                    self.count_label.config(text=f"已选: {len(self.selected_regions)}")

                    # 标记已选区域
                    self.mark_selected_region(region)

                    # 单区域模式下自动完成
                    if self.mode == "single":
                        self.finish_selection()
                    
    def on_mouse_move(self, event):
        """鼠标移动事件"""
        # 更新坐标信息
        self.coord_label.config(text=f"坐标: ({event.x}, {event.y})")
        
    def on_right_click(self, event):
        """右键点击事件"""
        self.finish_selection()
        
    def on_escape(self, event):
        """ESC键事件"""
        self.cancel_selection()
        
    def on_enter(self, event):
        """回车键事件"""
        self.finish_selection()
        
    def on_delete(self, event):
        """删除键事件"""
        if self.selected_regions:
            # 删除最后一个选择
            self.selected_regions.pop()
            self.count_label.config(text=f"已选: {len(self.selected_regions)}")
            # 重新绘制
            self.redraw_selections()
            
    def mark_selected_region(self, region):
        """标记已选区域"""
        # 绘制已选区域边框
        self.canvas.create_rectangle(
            region['x'], region['y'],
            region['x'] + region['width'],
            region['y'] + region['height'],
            outline='green', width=3, fill='', stipple='gray25',
            tags='selected'
        )
        
        # 添加区域标签
        self.canvas.create_text(
            region['x'] + 5, region['y'] + 5,
            text=region['name'], fill='green',
            font=('Arial', 12, 'bold'),
            anchor=tk.NW, tags='selected'
        )
        
    def redraw_selections(self):
        """重新绘制所有选择"""
        # 清除已选标记
        self.canvas.delete('selected')
        
        # 重新绘制所有选择
        for region in self.selected_regions:
            self.mark_selected_region(region)
            
    def finish_selection(self):
        """完成选择"""
        self.is_selecting = False
        
        # 关闭覆盖窗口
        if self.overlay_window:
            self.overlay_window.destroy()
            
        # 回调通知
        if self.callback:
            self.callback(self.selected_regions.copy())
            
    def cancel_selection(self):
        """取消选择"""
        self.is_selecting = False
        self.selected_regions.clear()
        
        # 关闭覆盖窗口
        if self.overlay_window:
            self.overlay_window.destroy()
            
        # 回调通知
        if self.callback:
            self.callback([])
            
    def get_selected_regions(self):
        """获取选择的区域"""
        return self.selected_regions.copy()

    def detect_grid_regions(self, left, top, right, bottom):
        """检测网格区域（自动回正为正方形，智能翻转）"""
        grid_regions = []

        # 使用设置的网格行列数
        original_rows, original_cols = self.grid_rows, self.grid_cols

        # 计算原始区域尺寸
        original_width = right - left
        original_height = bottom - top

        # 智能翻转：判断是否需要交换行列
        # 计算两种方向的适配度
        fit_normal = self._calculate_grid_fit(original_width, original_height, original_rows, original_cols)
        fit_flipped = self._calculate_grid_fit(original_width, original_height, original_cols, original_rows)

        # 选择适配度更好的方向
        if fit_flipped > fit_normal:
            rows, cols = original_cols, original_rows
            flipped = True
        else:
            rows, cols = original_rows, original_cols
            flipped = False

        # 计算理想的单元格大小（正方形）
        ideal_cell_width = original_width / cols
        ideal_cell_height = original_height / rows

        # 选择较小的尺寸作为正方形边长，确保所有网格都在选择区域内
        cell_size = min(ideal_cell_width, ideal_cell_height)

        # 计算实际网格区域尺寸
        actual_grid_width = cell_size * cols
        actual_grid_height = cell_size * rows

        # 计算居中偏移，使网格在选择区域中居中
        offset_x = (original_width - actual_grid_width) / 2
        offset_y = (original_height - actual_grid_height) / 2

        # 调整起始坐标
        grid_left = left + offset_x
        grid_top = top + offset_y

        # 存储翻转信息
        self.last_grid_flipped = flipped

        # 生成网格区域（正方形单元格）
        for row in range(rows):
            for col in range(cols):
                cell_left = grid_left + col * cell_size
                cell_top = grid_top + row * cell_size
                cell_right = cell_left + cell_size
                cell_bottom = cell_top + cell_size

                region = {
                    'x': int(cell_left),
                    'y': int(cell_top),
                    'width': int(cell_size),
                    'height': int(cell_size),
                    'name': f'{self.current_zone_name}[{row+1},{col+1}]',
                    'grid_row': row,
                    'grid_col': col,
                    'zone_name': self.current_zone_name,
                    'is_square': True  # 标记为正方形
                }

                grid_regions.append(region)

        return grid_regions

    def _calculate_grid_fit(self, width, height, rows, cols):
        """计算网格适配度（值越大适配度越好）"""
        # 计算每个单元格的理想尺寸
        cell_width = width / cols
        cell_height = height / rows

        # 计算正方形单元格的边长（取较小值）
        cell_size = min(cell_width, cell_height)

        # 计算利用率（实际使用的面积 / 总面积）
        used_width = cell_size * cols
        used_height = cell_size * rows
        used_area = used_width * used_height
        total_area = width * height
        utilization = used_area / total_area

        # 计算形状匹配度（越接近正方形越好）
        aspect_ratio = max(cell_width, cell_height) / min(cell_width, cell_height)
        shape_score = 1.0 / aspect_ratio  # 正方形时为1，越扁越小

        # 综合评分：利用率 * 形状匹配度
        return utilization * shape_score

    def draw_grid_regions(self, grid_regions, zone_name="网格"):
        """绘制网格区域"""
        # 为不同区域使用不同颜色
        zone_colors = ['blue', 'green', 'purple', 'orange', 'brown', 'pink']
        zone_index = len(self.grid_zones) - 1
        color = zone_colors[zone_index % len(zone_colors)]

        for i, region in enumerate(grid_regions):
            x1 = region['x']
            y1 = region['y']
            x2 = x1 + region['width']
            y2 = y1 + region['height']

            # 绘制网格边框
            self.canvas.create_rectangle(
                x1, y1, x2, y2,
                outline=color, width=1, fill='', stipple='gray25',
                tags=f"grid_{zone_name}"
            )

            # 在中心显示网格编号（只显示部分，避免太密集）
            if i % 15 == 0:  # 每15个显示一个编号
                center_x = x1 + region['width'] // 2
                center_y = y1 + region['height'] // 2
                self.canvas.create_text(
                    center_x, center_y,
                    text=f"{region['grid_row']+1},{region['grid_col']+1}",
                    fill=color, font=('Arial', 8),
                    tags=f"grid_{zone_name}"
                )

        # 在网格区域左上角显示区域名称和回正信息
        if grid_regions:
            first_region = grid_regions[0]
            cell_size = first_region['width']

            # 获取实际网格尺寸
            actual_rows = len(set(r['grid_row'] for r in grid_regions))
            actual_cols = len(set(r['grid_col'] for r in grid_regions))

            # 检查是否翻转
            flipped = getattr(self, 'last_grid_flipped', False)
            flip_indicator = "🔄" if flipped else ""

            # 主标题
            self.canvas.create_text(
                first_region['x'] + 20, first_region['y'] + 10,
                text=f"{zone_name}({actual_rows}×{actual_cols}){flip_indicator}",
                fill=color, font=('Arial', 12, 'bold'),
                tags=f"grid_{zone_name}"
            )

            # 回正信息
            info_text = f"正方形 {cell_size}×{cell_size}px"
            if flipped:
                info_text += " (智能翻转)"

            self.canvas.create_text(
                first_region['x'] + 20, first_region['y'] + 30,
                text=info_text,
                fill=color, font=('Arial', 10),
                tags=f"grid_{zone_name}"
            )

    def set_game_type(self, game_type):
        """设置游戏类型（用于网格检测）"""
        self.game_type = game_type

    def set_grid_size(self, rows, cols):
        """设置网格大小"""
        self.grid_rows = max(1, int(rows))
        self.grid_cols = max(1, int(cols))

    def get_grid_zones(self):
        """获取所有网格区域"""
        return self.grid_zones.copy()

    def clear_grid_zone(self, zone_name):
        """清除指定的网格区域"""
        # 从画布上删除该区域的图形
        if self.canvas:
            self.canvas.delete(f"grid_{zone_name}")

        # 从网格区域列表中删除
        self.grid_zones = [zone for zone in self.grid_zones if zone['name'] != zone_name]

        # 从选择区域中删除该区域的所有网格
        self.selected_regions = [
            region for region in self.selected_regions
            if not (hasattr(region, 'zone_name') and region.get('zone_name') == zone_name)
        ]

        # 更新计数
        if hasattr(self, 'count_label'):
            zone_count = len(self.grid_zones)
            self.count_label.config(text=f"已选: {len(self.selected_regions)} ({zone_count}个网格区域)")
