#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
管理员权限管理器
处理管理员权限获取和窗口控制
"""

import sys
import os
import ctypes
import subprocess
from ctypes import wintypes
import win32gui
import win32process
import win32con
import win32api
from typing import List, Dict, Optional, Tuple

class AdminManager:
    """管理员权限管理器"""
    
    def __init__(self):
        self.is_admin = self.check_admin_privileges()
        
    def check_admin_privileges(self) -> bool:
        """检查是否有管理员权限"""
        try:
            return ctypes.windll.shell32.IsUserAnAdmin()
        except:
            return False
            
    def request_admin_privileges(self) -> bool:
        """请求管理员权限"""
        if self.is_admin:
            return True
            
        try:
            # 重新启动程序并请求管理员权限
            script_path = sys.argv[0]
            params = ' '.join(sys.argv[1:])
            
            # 使用ShellExecute请求管理员权限
            result = ctypes.windll.shell32.ShellExecuteW(
                None, 
                "runas", 
                sys.executable, 
                f'"{script_path}" {params}',
                None, 
                1
            )
            
            if result > 32:
                # 成功启动新进程，退出当前进程
                sys.exit(0)
            else:
                return False
                
        except Exception as e:
            print(f"请求管理员权限失败: {e}")
            return False
            
    def get_admin_status_info(self) -> Dict[str, any]:
        """获取管理员状态信息"""
        return {
            'is_admin': self.is_admin,
            'user_name': os.getlogin(),
            'process_id': os.getpid(),
            'executable_path': sys.executable
        }

class WindowManager:
    """窗口管理器"""
    
    def __init__(self):
        self.target_windows = []
        
    def find_windows_by_title(self, title_pattern: str, exact_match: bool = False) -> List[Dict]:
        """根据标题查找窗口"""
        windows = []
        
        def enum_windows_callback(hwnd, lParam):
            if win32gui.IsWindowVisible(hwnd):
                window_title = win32gui.GetWindowText(hwnd)
                
                # 检查标题匹配
                if exact_match:
                    match = window_title == title_pattern
                else:
                    match = title_pattern.lower() in window_title.lower()
                    
                if match:
                    # 获取窗口信息
                    try:
                        rect = win32gui.GetWindowRect(hwnd)
                        pid = win32process.GetWindowThreadProcessId(hwnd)[1]
                        
                        window_info = {
                            'hwnd': hwnd,
                            'title': window_title,
                            'rect': rect,
                            'pid': pid,
                            'x': rect[0],
                            'y': rect[1],
                            'width': rect[2] - rect[0],
                            'height': rect[3] - rect[1]
                        }
                        
                        # 尝试获取进程名
                        try:
                            process_handle = win32api.OpenProcess(
                                win32con.PROCESS_QUERY_INFORMATION | win32con.PROCESS_VM_READ,
                                False,
                                pid
                            )
                            process_name = win32process.GetModuleFileNameEx(process_handle, 0)
                            window_info['process_name'] = os.path.basename(process_name)
                            win32api.CloseHandle(process_handle)
                        except:
                            window_info['process_name'] = 'Unknown'
                            
                        windows.append(window_info)
                        
                    except Exception as e:
                        print(f"获取窗口信息失败: {e}")
                        
            return True
            
        try:
            win32gui.EnumWindows(enum_windows_callback, None)
        except Exception as e:
            print(f"枚举窗口失败: {e}")
            
        return windows
        
    def find_mumu_windows(self) -> List[Dict]:
        """查找MuMu模拟器窗口"""
        mumu_patterns = [
            "MuMu安卓设备",
            "MuMu模拟器",
            "MuMu Player",
            "MuMu",
            "网易MuMu"
        ]
        
        all_mumu_windows = []
        
        for pattern in mumu_patterns:
            windows = self.find_windows_by_title(pattern, exact_match=False)
            for window in windows:
                # 避免重复添加
                if not any(w['hwnd'] == window['hwnd'] for w in all_mumu_windows):
                    all_mumu_windows.append(window)
                    
        return all_mumu_windows
        
    def set_window_topmost(self, hwnd: int, topmost: bool = True) -> bool:
        """设置窗口置顶"""
        try:
            if topmost:
                result = win32gui.SetWindowPos(
                    hwnd,
                    win32con.HWND_TOPMOST,
                    0, 0, 0, 0,
                    win32con.SWP_NOMOVE | win32con.SWP_NOSIZE
                )
            else:
                result = win32gui.SetWindowPos(
                    hwnd,
                    win32con.HWND_NOTOPMOST,
                    0, 0, 0, 0,
                    win32con.SWP_NOMOVE | win32con.SWP_NOSIZE
                )
                
            return result != 0
            
        except Exception as e:
            print(f"设置窗口置顶失败: {e}")
            return False
            
    def bring_window_to_front(self, hwnd: int) -> bool:
        """将窗口带到前台"""
        try:
            # 先显示窗口
            win32gui.ShowWindow(hwnd, win32con.SW_RESTORE)
            
            # 设置为前台窗口
            result = win32gui.SetForegroundWindow(hwnd)
            
            return result != 0
            
        except Exception as e:
            print(f"将窗口带到前台失败: {e}")
            return False
            
    def get_window_rect(self, hwnd: int) -> Optional[Tuple[int, int, int, int]]:
        """获取窗口矩形"""
        try:
            return win32gui.GetWindowRect(hwnd)
        except Exception as e:
            print(f"获取窗口矩形失败: {e}")
            return None
            
    def move_window(self, hwnd: int, x: int, y: int, width: int = None, height: int = None) -> bool:
        """移动窗口"""
        try:
            if width is None or height is None:
                # 只移动位置，不改变大小
                rect = self.get_window_rect(hwnd)
                if rect:
                    width = rect[2] - rect[0]
                    height = rect[3] - rect[1]
                else:
                    return False
                    
            result = win32gui.SetWindowPos(
                hwnd,
                0,
                x, y, width, height,
                win32con.SWP_NOZORDER
            )
            
            return result != 0
            
        except Exception as e:
            print(f"移动窗口失败: {e}")
            return False
            
    def resize_window(self, hwnd: int, width: int, height: int) -> bool:
        """调整窗口大小"""
        try:
            rect = self.get_window_rect(hwnd)
            if not rect:
                return False
                
            result = win32gui.SetWindowPos(
                hwnd,
                0,
                rect[0], rect[1], width, height,
                win32con.SWP_NOZORDER
            )
            
            return result != 0
            
        except Exception as e:
            print(f"调整窗口大小失败: {e}")
            return False
            
    def minimize_window(self, hwnd: int) -> bool:
        """最小化窗口"""
        try:
            win32gui.ShowWindow(hwnd, win32con.SW_MINIMIZE)
            return True
        except Exception as e:
            print(f"最小化窗口失败: {e}")
            return False
            
    def maximize_window(self, hwnd: int) -> bool:
        """最大化窗口"""
        try:
            win32gui.ShowWindow(hwnd, win32con.SW_MAXIMIZE)
            return True
        except Exception as e:
            print(f"最大化窗口失败: {e}")
            return False
            
    def restore_window(self, hwnd: int) -> bool:
        """恢复窗口"""
        try:
            win32gui.ShowWindow(hwnd, win32con.SW_RESTORE)
            return True
        except Exception as e:
            print(f"恢复窗口失败: {e}")
            return False

class MuMuWindowManager:
    """MuMu模拟器窗口管理器"""
    
    def __init__(self):
        self.admin_manager = AdminManager()
        self.window_manager = WindowManager()
        self.current_mumu_window = None
        
    def ensure_admin_privileges(self) -> bool:
        """确保有管理员权限"""
        if not self.admin_manager.is_admin:
            print("需要管理员权限来控制MuMu模拟器窗口")
            return self.admin_manager.request_admin_privileges()
        return True
        
    def find_and_select_mumu_window(self) -> Optional[Dict]:
        """查找并选择MuMu窗口"""
        mumu_windows = self.window_manager.find_mumu_windows()
        
        if not mumu_windows:
            print("未找到MuMu模拟器窗口")
            return None
            
        if len(mumu_windows) == 1:
            # 只有一个窗口，直接选择
            self.current_mumu_window = mumu_windows[0]
            print(f"找到MuMu窗口: {self.current_mumu_window['title']}")
            return self.current_mumu_window
        else:
            # 多个窗口，显示选择列表
            print("找到多个MuMu窗口:")
            for i, window in enumerate(mumu_windows):
                print(f"{i+1}. {window['title']} (PID: {window['pid']})")
                
            # 这里可以添加GUI选择界面，暂时选择第一个
            self.current_mumu_window = mumu_windows[0]
            print(f"自动选择第一个窗口: {self.current_mumu_window['title']}")
            return self.current_mumu_window
            
    def setup_mumu_window(self, bring_to_front: bool = True, set_topmost: bool = True) -> bool:
        """设置MuMu窗口"""
        if not self.current_mumu_window:
            if not self.find_and_select_mumu_window():
                return False
                
        hwnd = self.current_mumu_window['hwnd']
        
        try:
            success = True
            
            # 将窗口带到前台
            if bring_to_front:
                if self.window_manager.bring_window_to_front(hwnd):
                    print("✓ MuMu窗口已带到前台")
                else:
                    print("✗ 将MuMu窗口带到前台失败")
                    success = False
                    
            # 设置窗口置顶
            if set_topmost:
                if self.window_manager.set_window_topmost(hwnd, True):
                    print("✓ MuMu窗口已设置为置顶")
                else:
                    print("✗ 设置MuMu窗口置顶失败")
                    success = False
                    
            return success
            
        except Exception as e:
            print(f"设置MuMu窗口失败: {e}")
            return False
            
    def cleanup_mumu_window(self) -> bool:
        """清理MuMu窗口设置（取消置顶）"""
        if not self.current_mumu_window:
            return True
            
        hwnd = self.current_mumu_window['hwnd']
        
        try:
            if self.window_manager.set_window_topmost(hwnd, False):
                print("✓ 已取消MuMu窗口置顶")
                return True
            else:
                print("✗ 取消MuMu窗口置顶失败")
                return False
                
        except Exception as e:
            print(f"清理MuMu窗口设置失败: {e}")
            return False
            
    def get_mumu_window_info(self) -> Optional[Dict]:
        """获取当前MuMu窗口信息"""
        return self.current_mumu_window
        
    def get_mumu_coordinate_system(self) -> Optional[Dict]:
        """获取MuMu窗口的相对坐标系信息"""
        if not self.current_mumu_window:
            return None
            
        window_rect = self.current_mumu_window['rect']
        
        return {
            'window_x': window_rect[0],
            'window_y': window_rect[1],
            'window_width': window_rect[2] - window_rect[0],
            'window_height': window_rect[3] - window_rect[1],
            'client_area': {
                'x': window_rect[0],
                'y': window_rect[1],
                'width': window_rect[2] - window_rect[0],
                'height': window_rect[3] - window_rect[1]
            }
        }
        
    def convert_to_screen_coordinates(self, relative_x: int, relative_y: int) -> Tuple[int, int]:
        """将相对坐标转换为屏幕坐标"""
        if not self.current_mumu_window:
            return relative_x, relative_y
            
        window_rect = self.current_mumu_window['rect']
        screen_x = window_rect[0] + relative_x
        screen_y = window_rect[1] + relative_y
        
        return screen_x, screen_y
        
    def convert_to_relative_coordinates(self, screen_x: int, screen_y: int) -> Tuple[int, int]:
        """将屏幕坐标转换为相对坐标"""
        if not self.current_mumu_window:
            return screen_x, screen_y
            
        window_rect = self.current_mumu_window['rect']
        relative_x = screen_x - window_rect[0]
        relative_y = screen_y - window_rect[1]
        
        return relative_x, relative_y
