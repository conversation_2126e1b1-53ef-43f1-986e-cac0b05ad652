#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图像识别模块
支持数字识别、空白检测，防止6/9混淆
"""

import cv2
import numpy as np
import pytesseract
from PIL import Image, ImageEnhance, ImageFilter
import pyautogui
from typing import List, Tuple, Dict, Optional, Any
import re

class ImageRecognizer:
    """图像识别器类"""
    
    def __init__(self):
        # Tesseract配置
        self.tesseract_config = '--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789'
        
        # 数字模板（用于模板匹配）
        self.digit_templates = {}
        
        # 识别参数
        self.recognition_params = {
            'min_confidence': 60,
            'blur_kernel': (3, 3),
            'morph_kernel': (2, 2),
            'threshold_method': cv2.THRESH_BINARY + cv2.THRESH_OTSU,
            'denoise_strength': 10
        }
        
    def capture_region(self, x: int, y: int, width: int, height: int) -> np.ndarray:
        """捕获指定区域的图像"""
        try:
            # 使用pyautogui截图
            screenshot = pyautogui.screenshot(region=(x, y, width, height))
            
            # 转换为OpenCV格式
            image = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
            
            return image
            
        except Exception as e:
            raise Exception(f"捕获区域失败: {e}")
            
    def preprocess_image(self, image: np.ndarray, enhance_contrast: bool = True) -> np.ndarray:
        """预处理图像"""
        try:
            # 转换为灰度图
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image.copy()
                
            # 增强对比度
            if enhance_contrast:
                # 使用CLAHE (Contrast Limited Adaptive Histogram Equalization)
                clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
                gray = clahe.apply(gray)
                
            # 降噪
            gray = cv2.medianBlur(gray, 3)
            
            # 二值化
            _, binary = cv2.threshold(gray, 0, 255, self.recognition_params['threshold_method'])
            
            # 形态学操作
            kernel = cv2.getStructuringElement(cv2.MORPH_RECT, self.recognition_params['morph_kernel'])
            binary = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
            
            return binary
            
        except Exception as e:
            raise Exception(f"图像预处理失败: {e}")
            
    def recognize_digit(self, image: np.ndarray, position: Tuple[int, int] = None) -> Dict[str, Any]:
        """识别单个数字"""
        try:
            # 预处理图像
            processed = self.preprocess_image(image)
            
            # 使用Tesseract识别
            result = pytesseract.image_to_data(
                processed,
                config=self.tesseract_config,
                output_type=pytesseract.Output.DICT
            )
            
            # 解析结果
            recognized_text = ""
            confidence = 0
            
            for i, text in enumerate(result['text']):
                if text.strip() and result['conf'][i] > self.recognition_params['min_confidence']:
                    recognized_text += text.strip()
                    confidence = max(confidence, result['conf'][i])
                    
            # 清理识别结果
            cleaned_text = self.clean_recognition_result(recognized_text)
            
            # 防止6/9混淆
            if cleaned_text in ['6', '9']:
                cleaned_text = self.resolve_6_9_confusion(image, cleaned_text)
                
            return {
                'text': cleaned_text,
                'confidence': confidence,
                'is_digit': cleaned_text.isdigit(),
                'is_empty': len(cleaned_text) == 0,
                'position': position,
                'raw_result': result
            }
            
        except Exception as e:
            return {
                'text': '',
                'confidence': 0,
                'is_digit': False,
                'is_empty': True,
                'position': position,
                'error': str(e)
            }
            
    def clean_recognition_result(self, text: str) -> str:
        """清理识别结果"""
        # 移除非数字字符
        cleaned = re.sub(r'[^0-9]', '', text)
        
        # 如果结果为空，返回空字符串
        if not cleaned:
            return ''
            
        # 如果有多个数字，只取第一个（通常是最可能的）
        return cleaned[0] if cleaned else ''
        
    def resolve_6_9_confusion(self, image: np.ndarray, recognized_digit: str) -> str:
        """解决6和9的混淆问题"""
        try:
            # 分析图像的方向特征
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY) if len(image.shape) == 3 else image
            
            # 计算图像的重心
            moments = cv2.moments(gray)
            if moments['m00'] != 0:
                cx = int(moments['m10'] / moments['m00'])
                cy = int(moments['m01'] / moments['m00'])
                
                # 分析上下部分的像素密度
                height, width = gray.shape
                upper_half = gray[:height//2, :]
                lower_half = gray[height//2:, :]
                
                upper_density = np.sum(upper_half < 128) / (upper_half.size)
                lower_density = np.sum(lower_half < 128) / (lower_half.size)
                
                # 6的特征：下部分密度更高（有闭合的圆圈）
                # 9的特征：上部分密度更高（有闭合的圆圈）
                if upper_density > lower_density * 1.2:
                    return '9'
                elif lower_density > upper_density * 1.2:
                    return '6'
                    
            # 如果无法确定，返回原始识别结果
            return recognized_digit
            
        except Exception:
            return recognized_digit
            
    def recognize_grid(self, image: np.ndarray, grid_size: Tuple[int, int], 
                      cell_padding: int = 2) -> List[List[Dict[str, Any]]]:
        """识别网格中的数字"""
        try:
            rows, cols = grid_size
            height, width = image.shape[:2]
            
            cell_width = width // cols
            cell_height = height // rows
            
            results = []
            
            for row in range(rows):
                row_results = []
                for col in range(cols):
                    # 计算单元格位置
                    x = col * cell_width + cell_padding
                    y = row * cell_height + cell_padding
                    w = cell_width - 2 * cell_padding
                    h = cell_height - 2 * cell_padding
                    
                    # 提取单元格图像
                    cell_image = image[y:y+h, x:x+w]
                    
                    # 识别单元格内容
                    result = self.recognize_digit(cell_image, position=(row, col))
                    result['grid_position'] = (row, col)
                    result['pixel_position'] = (x, y, w, h)
                    
                    row_results.append(result)
                    
                results.append(row_results)
                
            return results
            
        except Exception as e:
            raise Exception(f"网格识别失败: {e}")
            
    def detect_empty_cells(self, image: np.ndarray, threshold: float = 0.1) -> bool:
        """检测单元格是否为空"""
        try:
            # 转换为灰度图
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY) if len(image.shape) == 3 else image
            
            # 计算非白色像素的比例
            non_white_pixels = np.sum(gray < 240)
            total_pixels = gray.size
            
            ratio = non_white_pixels / total_pixels
            
            return ratio < threshold
            
        except Exception:
            return True
            
    def enhance_image_quality(self, image: np.ndarray) -> np.ndarray:
        """增强图像质量"""
        try:
            # 转换为PIL图像
            pil_image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
            
            # 增强对比度
            enhancer = ImageEnhance.Contrast(pil_image)
            pil_image = enhancer.enhance(1.5)
            
            # 增强锐度
            enhancer = ImageEnhance.Sharpness(pil_image)
            pil_image = enhancer.enhance(1.2)
            
            # 应用锐化滤镜
            pil_image = pil_image.filter(ImageFilter.SHARPEN)
            
            # 转换回OpenCV格式
            enhanced = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
            
            return enhanced
            
        except Exception:
            return image
            
    def validate_recognition_result(self, result: Dict[str, Any], 
                                  expected_range: Tuple[int, int] = (1, 9)) -> bool:
        """验证识别结果的有效性"""
        try:
            if not result['is_digit']:
                return result['is_empty']  # 空白是有效的
                
            digit = int(result['text'])
            min_val, max_val = expected_range
            
            return min_val <= digit <= max_val and result['confidence'] >= self.recognition_params['min_confidence']
            
        except Exception:
            return False
            
    def get_recognition_stats(self, results: List[List[Dict[str, Any]]]) -> Dict[str, Any]:
        """获取识别统计信息"""
        try:
            total_cells = sum(len(row) for row in results)
            recognized_digits = 0
            empty_cells = 0
            low_confidence = 0
            total_confidence = 0
            
            for row in results:
                for cell in row:
                    if cell['is_digit']:
                        recognized_digits += 1
                        total_confidence += cell['confidence']
                    elif cell['is_empty']:
                        empty_cells += 1
                        
                    if cell['confidence'] < self.recognition_params['min_confidence']:
                        low_confidence += 1
                        
            avg_confidence = total_confidence / recognized_digits if recognized_digits > 0 else 0
            
            return {
                'total_cells': total_cells,
                'recognized_digits': recognized_digits,
                'empty_cells': empty_cells,
                'low_confidence_cells': low_confidence,
                'average_confidence': avg_confidence,
                'recognition_rate': (recognized_digits + empty_cells) / total_cells * 100
            }
            
        except Exception as e:
            return {'error': str(e)}
            
    def save_debug_image(self, image: np.ndarray, results: List[List[Dict[str, Any]]], 
                        filename: str = "debug_recognition.png"):
        """保存调试图像（标注识别结果）"""
        try:
            debug_image = image.copy()
            
            for row_idx, row in enumerate(results):
                for col_idx, cell in enumerate(row):
                    if 'pixel_position' in cell:
                        x, y, w, h = cell['pixel_position']
                        
                        # 绘制边框
                        color = (0, 255, 0) if cell['is_digit'] else (255, 0, 0) if not cell['is_empty'] else (128, 128, 128)
                        cv2.rectangle(debug_image, (x, y), (x+w, y+h), color, 2)
                        
                        # 添加文本标注
                        text = cell['text'] if cell['text'] else 'Empty'
                        cv2.putText(debug_image, text, (x+5, y+20), cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)
                        
                        # 添加置信度
                        conf_text = f"{cell['confidence']:.0f}%"
                        cv2.putText(debug_image, conf_text, (x+5, y+h-5), cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)
                        
            cv2.imwrite(filename, debug_image)
            
        except Exception as e:
            print(f"保存调试图像失败: {e}")
