#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主窗口GUI界面
"""

import tkinter as tk
from tkinter import ttk, messagebox
import json
import os

from utils.dpi_helper import configure_styles, DPIAwareWidget
from gui.game_selector import GameSelector
from gui.config_panel import ConfigPanel
from gui.region_selector import RegionSelector
from core.game_automation import GameAutomationManager
from core.game_plugin_system import GamePluginManager
from core.game_automation import GameAutomationManager
from core.game_plugin_system import GamePluginManager
from utils.admin_manager import MuMuWindowManager

class MainWindow(DPIAwareWidget):
    """主窗口类"""
    
    def __init__(self, root):
        super().__init__(root)
        self.root = root

        # 初始化管理器
        self.automation_manager = GameAutomationManager()
        self.plugin_manager = GamePluginManager()
        self.mumu_manager = MuMuWindowManager()

        # 加载插件
        self.plugin_manager.load_all_plugins()

        self.setup_window()
        self.create_widgets()
        
    def setup_window(self):
        """设置窗口属性"""
        self.root.title("游戏辅助工具 v1.0")
        self.root.geometry(f"{self.scaled(1200)}x{self.scaled(800)}")
        self.root.minsize(self.scaled(800), self.scaled(600))
        
        # 配置DPI样式
        self.scale = configure_styles(self.root)
        
        # 设置窗口图标（如果有的话）
        try:
            self.root.iconbitmap("icon.ico")
        except:
            pass
            
        # 窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
    def create_widgets(self):
        """创建界面控件"""
        # 创建主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=self.scaled(10), pady=self.scaled(10))
        
        # 创建标题
        title_label = ttk.Label(main_frame, text="游戏辅助工具", style='Heading.TLabel')
        title_label.pack(pady=(0, self.scaled(20)))
        
        # 创建主要内容区域
        content_frame = ttk.Frame(main_frame)
        content_frame.pack(fill=tk.BOTH, expand=True)
        
        # 左侧面板 - 游戏选择
        left_frame = ttk.LabelFrame(content_frame, text="游戏选择", padding=self.scaled_tuple(10, 10))
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, self.scaled(10)))
        
        # 中间面板 - 配置区域
        middle_frame = ttk.LabelFrame(content_frame, text="配置管理", padding=self.scaled_tuple(10, 10))
        middle_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, self.scaled(10)))
        
        # 右侧面板 - 框选配置和自动化
        right_frame = ttk.LabelFrame(content_frame, text="区域框选和自动化", padding=self.scaled_tuple(10, 10))
        right_frame.pack(side=tk.RIGHT, fill=tk.Y)

        # 创建右侧标签页
        right_notebook = ttk.Notebook(right_frame)
        right_notebook.pack(fill=tk.BOTH, expand=True)

        # 区域框选标签页
        region_tab = ttk.Frame(right_notebook)
        right_notebook.add(region_tab, text="区域框选")

        # 自动化控制标签页
        automation_tab = ttk.Frame(right_notebook)
        right_notebook.add(automation_tab, text="自动化控制")

        # 创建各个组件
        self.game_selector = GameSelector(left_frame, self.on_game_changed)
        self.config_panel = ConfigPanel(middle_frame, self.on_config_selected)
        self.region_selector = RegionSelector(region_tab, self.on_region_selected)

        # 创建自动化控制面板
        self.create_automation_panel(automation_tab)
        
        # 底部状态栏
        self.create_status_bar(main_frame)
        
    def create_status_bar(self, parent):
        """创建状态栏"""
        status_frame = ttk.Frame(parent)
        status_frame.pack(fill=tk.X, pady=(self.scaled(10), 0))
        
        # 状态标签
        self.status_var = tk.StringVar(value="就绪")
        status_label = ttk.Label(status_frame, textvariable=self.status_var)
        status_label.pack(side=tk.LEFT)
        
        # 版本信息
        version_label = ttk.Label(status_frame, text="v1.0")
        version_label.pack(side=tk.RIGHT)

    def create_automation_panel(self, parent):
        """创建自动化控制面板"""
        # MuMu窗口管理
        mumu_frame = ttk.LabelFrame(parent, text="MuMu窗口管理")
        mumu_frame.pack(fill=tk.X, pady=(0, self.scaled(10)))

        mumu_button_frame = ttk.Frame(mumu_frame)
        mumu_button_frame.pack(fill=tk.X, pady=self.scaled(5))

        ttk.Button(mumu_button_frame, text="查找MuMu窗口", command=self.find_mumu_window).pack(side=tk.LEFT, padx=(0, self.scaled(5)))
        ttk.Button(mumu_button_frame, text="设置置顶", command=self.setup_mumu_window).pack(side=tk.LEFT, padx=(0, self.scaled(5)))
        ttk.Button(mumu_button_frame, text="取消置顶", command=self.cleanup_mumu_window).pack(side=tk.LEFT)

        self.mumu_status_var = tk.StringVar(value="未连接MuMu窗口")
        mumu_status_label = ttk.Label(mumu_frame, textvariable=self.mumu_status_var)
        mumu_status_label.pack(pady=self.scaled(5))

        # 自动化状态
        status_frame = ttk.LabelFrame(parent, text="自动化状态")
        status_frame.pack(fill=tk.X, pady=(0, self.scaled(10)))

        self.automation_status_var = tk.StringVar(value="未启用")
        status_label = ttk.Label(status_frame, textvariable=self.automation_status_var)
        status_label.pack(pady=self.scaled(5))

        # 控制按钮
        control_frame = ttk.LabelFrame(parent, text="控制操作")
        control_frame.pack(fill=tk.X, pady=(0, self.scaled(10)))

        button_frame = ttk.Frame(control_frame)
        button_frame.pack(fill=tk.X, pady=self.scaled(5))

        ttk.Button(button_frame, text="启用自动化", command=self.enable_automation).pack(side=tk.LEFT, padx=(0, self.scaled(5)))
        ttk.Button(button_frame, text="禁用自动化", command=self.disable_automation).pack(side=tk.LEFT, padx=(0, self.scaled(5)))

        button_frame2 = ttk.Frame(control_frame)
        button_frame2.pack(fill=tk.X, pady=(self.scaled(5), 0))

        ttk.Button(button_frame2, text="执行一步", command=self.execute_one_step).pack(side=tk.LEFT, padx=(0, self.scaled(5)))
        ttk.Button(button_frame2, text="自动求解", command=self.auto_solve).pack(side=tk.LEFT)

        # 操作队列
        queue_frame = ttk.LabelFrame(parent, text="操作队列")
        queue_frame.pack(fill=tk.BOTH, expand=True)

        # 创建列表框显示操作队列
        self.queue_listbox = tk.Listbox(queue_frame, height=8)
        queue_scrollbar = ttk.Scrollbar(queue_frame, orient=tk.VERTICAL, command=self.queue_listbox.yview)
        self.queue_listbox.config(yscrollcommand=queue_scrollbar.set)

        self.queue_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(self.scaled(5), 0), pady=self.scaled(5))
        queue_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=self.scaled(5))

        # 更新自动化状态
        self.update_automation_status()
        
    def on_game_changed(self, game_type):
        """游戏类型改变回调"""
        self.status_var.set(f"选择游戏: {game_type}")
        self.config_panel.set_game_type(game_type)
        self.region_selector.set_game_type(game_type)
        
    def on_config_selected(self, config_name):
        """配置选择回调"""
        self.status_var.set(f"选择配置: {config_name}")
        
    def on_region_selected(self, region_info):
        """区域选择回调"""
        self.status_var.set(f"选择区域: {region_info}")

    def enable_automation(self):
        """启用自动化"""
        try:
            current_game = self.game_selector.get_selected_game()
            if current_game:
                self.automation_manager.enable_automation(current_game)
                self.update_automation_status()
                self.status_var.set(f"已启用 {current_game} 自动化")
            else:
                messagebox.showwarning("警告", "请先选择游戏类型")
        except Exception as e:
            messagebox.showerror("错误", f"启用自动化失败: {e}")

    def disable_automation(self):
        """禁用自动化"""
        try:
            current_game = self.game_selector.get_selected_game()
            if current_game:
                self.automation_manager.disable_automation(current_game)
                self.update_automation_status()
                self.status_var.set(f"已禁用 {current_game} 自动化")
        except Exception as e:
            messagebox.showerror("错误", f"禁用自动化失败: {e}")

    def execute_one_step(self):
        """执行一步操作"""
        try:
            # 获取当前配置
            current_config = self.config_panel.get_selected_config()
            if not current_config:
                messagebox.showwarning("警告", "请先选择配置")
                return

            # 执行识别
            from core.recognition_manager import RecognitionManager
            recognition_manager = RecognitionManager()

            if recognition_manager.load_config(current_config['id']):
                result = recognition_manager.recognize_game()

                if 'error' in result:
                    messagebox.showerror("错误", f"识别失败: {result['error']}")
                    return

                # 生成并执行一步操作
                current_game = self.game_selector.get_selected_game()
                self.automation_manager.set_current_game(current_game)

                automation = self.automation_manager.get_automation(current_game)
                if automation:
                    game_state = automation.analyze_game_state(result)
                    actions = automation.generate_solution(game_state)

                    if actions:
                        # 只执行第一个操作
                        automation.add_action(actions[0])
                        success = automation.execute_action_queue()

                        if success:
                            self.status_var.set("执行一步操作成功")
                        else:
                            self.status_var.set("执行操作失败")
                    else:
                        messagebox.showinfo("信息", "没有可执行的操作")

                self.update_automation_status()

        except Exception as e:
            messagebox.showerror("错误", f"执行操作失败: {e}")

    def auto_solve(self):
        """自动求解"""
        try:
            # 获取当前配置
            current_config = self.config_panel.get_selected_config()
            if not current_config:
                messagebox.showwarning("警告", "请先选择配置")
                return

            # 确认对话框
            if not messagebox.askyesno("确认", "确定要开始自动求解吗？\n这将自动执行所有操作直到完成。"):
                return

            # 执行识别
            from core.recognition_manager import RecognitionManager
            recognition_manager = RecognitionManager()

            if recognition_manager.load_config(current_config['id']):
                result = recognition_manager.recognize_game()

                if 'error' in result:
                    messagebox.showerror("错误", f"识别失败: {result['error']}")
                    return

                # 生成并执行完整解决方案
                current_game = self.game_selector.get_selected_game()
                self.automation_manager.set_current_game(current_game)

                success = self.automation_manager.generate_and_execute_solution(result)

                if success:
                    self.status_var.set("自动求解完成")
                    messagebox.showinfo("成功", "自动求解完成")
                else:
                    self.status_var.set("自动求解失败")
                    messagebox.showerror("失败", "自动求解失败")

                self.update_automation_status()

        except Exception as e:
            messagebox.showerror("错误", f"自动求解失败: {e}")

    def update_automation_status(self):
        """更新自动化状态"""
        try:
            status = self.automation_manager.get_automation_status()
            current_game = status.get('current_game', '无')

            if current_game != '无':
                automation_info = status['automations'].get(current_game, {})
                is_enabled = automation_info.get('is_enabled', False)
                queue_length = automation_info.get('queue_length', 0)

                status_text = f"当前游戏: {current_game}\n"
                status_text += f"状态: {'已启用' if is_enabled else '未启用'}\n"
                status_text += f"队列长度: {queue_length}"

                self.automation_status_var.set(status_text)

                # 更新操作队列显示
                self.update_queue_display(current_game)
            else:
                self.automation_status_var.set("未选择游戏")
                self.queue_listbox.delete(0, tk.END)

        except Exception as e:
            self.automation_status_var.set(f"状态更新失败: {e}")

    def update_queue_display(self, game_type):
        """更新操作队列显示"""
        try:
            automation = self.automation_manager.get_automation(game_type)
            if automation:
                queue_info = automation.get_action_queue_info()
                actions = queue_info.get('actions', [])

                # 清空列表
                self.queue_listbox.delete(0, tk.END)

                # 添加操作到列表
                for i, action in enumerate(actions):
                    action_text = f"{i+1}. {action['type']}: {action['description']}"
                    self.queue_listbox.insert(tk.END, action_text)

        except Exception as e:
            print(f"更新队列显示失败: {e}")

    def find_mumu_window(self):
        """查找MuMu窗口"""
        try:
            if self.mumu_manager.find_and_select_mumu_window():
                window_info = self.mumu_manager.get_mumu_window_info()
                self.mumu_status_var.set(f"已连接: {window_info['title']}")
                self.status_var.set("MuMu窗口连接成功")
                messagebox.showinfo("成功", f"找到MuMu窗口:\n{window_info['title']}")
            else:
                self.mumu_status_var.set("未找到MuMu窗口")
                self.status_var.set("未找到MuMu窗口")
                messagebox.showwarning("警告", "未找到MuMu模拟器窗口\n请确保MuMu模拟器正在运行")
        except Exception as e:
            messagebox.showerror("错误", f"查找MuMu窗口失败: {e}")

    def setup_mumu_window(self):
        """设置MuMu窗口（置顶）"""
        try:
            if not self.mumu_manager.current_mumu_window:
                if not self.mumu_manager.find_and_select_mumu_window():
                    messagebox.showwarning("警告", "请先查找MuMu窗口")
                    return

            if self.mumu_manager.setup_mumu_window():
                self.status_var.set("MuMu窗口已设置为置顶")
                messagebox.showinfo("成功", "MuMu窗口已设置为置顶并带到前台")
            else:
                messagebox.showerror("失败", "设置MuMu窗口失败")
        except Exception as e:
            messagebox.showerror("错误", f"设置MuMu窗口失败: {e}")

    def cleanup_mumu_window(self):
        """清理MuMu窗口设置（取消置顶）"""
        try:
            if not self.mumu_manager.current_mumu_window:
                messagebox.showinfo("提示", "没有连接的MuMu窗口")
                return

            if self.mumu_manager.cleanup_mumu_window():
                self.status_var.set("已取消MuMu窗口置顶")
                messagebox.showinfo("成功", "已取消MuMu窗口置顶")
            else:
                messagebox.showerror("失败", "取消MuMu窗口置顶失败")
        except Exception as e:
            messagebox.showerror("错误", f"清理MuMu窗口设置失败: {e}")
        
    def on_closing(self):
        """窗口关闭事件"""
        try:
            # 清理MuMu窗口设置
            if hasattr(self, 'mumu_manager') and self.mumu_manager.current_mumu_window:
                self.mumu_manager.cleanup_mumu_window()
                print("已清理MuMu窗口设置")

            # 保存配置
            self.save_settings()
            self.root.destroy()
        except Exception as e:
            messagebox.showerror("错误", f"关闭程序时出错: {e}")
            self.root.destroy()
            
    def save_settings(self):
        """保存设置"""
        try:
            settings = {
                "window_geometry": self.root.geometry(),
                "last_game": getattr(self.game_selector, 'current_game', ''),
                "last_config": getattr(self.config_panel, 'current_config', '')
            }
            
            os.makedirs("data", exist_ok=True)
            with open("data/settings.json", "w", encoding="utf-8") as f:
                json.dump(settings, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"保存设置失败: {e}")
            
    def load_settings(self):
        """加载设置"""
        try:
            if os.path.exists("data/settings.json"):
                with open("data/settings.json", "r", encoding="utf-8") as f:
                    settings = json.load(f)
                    
                # 恢复窗口大小
                if "window_geometry" in settings:
                    self.root.geometry(settings["window_geometry"])
                    
                return settings
        except Exception as e:
            print(f"加载设置失败: {e}")
            
        return {}
