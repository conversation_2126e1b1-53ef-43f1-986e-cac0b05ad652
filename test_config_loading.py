#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置加载测试
诊断配置加载失败的问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.config_manager import ConfigManager
from core.recognition_manager import RecognitionManager

def test_config_manager():
    """测试配置管理器"""
    print("=== 配置管理器测试 ===")
    
    config_manager = ConfigManager()
    
    # 检查配置目录
    print(f"配置目录: {config_manager.config_dir}")
    print(f"配置目录存在: {os.path.exists(config_manager.config_dir)}")
    
    # 检查配置文件
    if os.path.exists(config_manager.config_dir):
        config_files = [f for f in os.listdir(config_manager.config_dir) if f.endswith('.json')]
        print(f"配置文件数量: {len(config_files)}")
        for file in config_files:
            print(f"  - {file}")
    
    # 检查内存中的配置
    print(f"内存中配置数量: {len(config_manager.configs)}")
    for config_id, config_data in config_manager.configs.items():
        print(f"  - {config_id}: {config_data.get('name', '未命名')} ({config_data.get('game_type', '未知')})")
    
    return config_manager

def test_specific_config():
    """测试特定配置"""
    print("\n=== 特定配置测试 ===")
    
    config_id = "e72a9de4-7da1-446b-8521-2740ba01bb2c"
    config_manager = ConfigManager()
    
    # 尝试加载配置
    config = config_manager.load_config(config_id)
    if config:
        print(f"✓ 配置加载成功")
        print(f"  名称: {config.get('name', '未命名')}")
        print(f"  游戏类型: {config.get('game_type', '未知')}")
        print(f"  区域数量: {len(config.get('regions', []))}")
        print(f"  版本: {config.get('version', '未知')}")
    else:
        print(f"✗ 配置加载失败")
        
        # 检查文件是否存在
        config_file = os.path.join(config_manager.config_dir, f"{config_id}.json")
        print(f"配置文件路径: {config_file}")
        print(f"文件存在: {os.path.exists(config_file)}")
        
        if os.path.exists(config_file):
            try:
                import json
                with open(config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                print(f"文件内容正常，包含 {len(data)} 个字段")
            except Exception as e:
                print(f"文件读取错误: {e}")
    
    return config

def test_recognition_manager():
    """测试识别管理器"""
    print("\n=== 识别管理器测试 ===")
    
    recognition_manager = RecognitionManager()
    config_id = "e72a9de4-7da1-446b-8521-2740ba01bb2c"
    
    # 测试加载配置
    success = recognition_manager.load_config(config_id)
    print(f"识别管理器加载配置: {'成功' if success else '失败'}")
    
    if success:
        print(f"当前配置: {recognition_manager.current_config.get('name', '未命名')}")
        print(f"游戏类型: {recognition_manager.current_game_type}")
        
        # 测试识别
        try:
            result = recognition_manager.recognize_game()
            if 'error' in result:
                print(f"识别失败: {result['error']}")
            else:
                print(f"识别成功，结果类型: {type(result)}")
        except Exception as e:
            print(f"识别异常: {e}")
    
    return recognition_manager

if __name__ == "__main__":
    try:
        config_manager = test_config_manager()
        config = test_specific_config()
        recognition_manager = test_recognition_manager()
        
        print("\n=== 测试总结 ===")
        print(f"配置管理器正常: {len(config_manager.configs) > 0}")
        print(f"特定配置加载: {config is not None}")
        print(f"识别管理器正常: {recognition_manager.current_config is not None}")
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
