# 游戏辅助工具功能说明

## 🔧 已修复的问题

### 1. 窗口退出自动取消置顶 ✅

**问题描述**：程序退出时没有自动取消MuMu窗口的置顶状态

**解决方案**：
- 在主程序 `main.py` 中添加了完善的窗口关闭事件处理
- 程序退出时会自动调用 `mumu_manager.cleanup_mumu_window()` 取消置顶
- 添加了清理状态的控制台输出，方便用户确认

**实现代码**：
```python
def on_closing():
    try:
        # 通过主窗口清理MuMu窗口设置
        if hasattr(app, 'mumu_manager') and app.mumu_manager.current_mumu_window:
            print("正在清理MuMu窗口设置...")
            if app.mumu_manager.cleanup_mumu_window():
                print("✓ MuMu窗口置顶已取消")
            else:
                print("✗ 取消MuMu窗口置顶失败")
```

### 2. 网格选框和多区域选框区别 ✅

**问题描述**：网格模式和多区域模式的选框行为完全一样，没有体现出区别

**解决方案**：

#### 🔲 多区域模式（Multi-Region Mode）
- **行为**：手动框选多个独立区域
- **操作**：每次拖拽选择一个区域，可以选择多个不同位置的区域
- **完成**：右键点击或ESC键完成选择
- **适用**：数织游戏的行提示区、列提示区等分离的区域

#### 🔳 网格模式（Grid Mode）
- **行为**：选择一个区域后自动分割成网格
- **操作**：只需拖拽选择一个大区域，程序自动分割成网格单元
- **完成**：可以选择多次，创建分区A、分区B等多个网格区域
- **自定义比例**：支持设置横×竖的网格比例（如9×9、15×15等）
- **适用**：数独、华容道等需要网格识别的游戏

**实现特点**：
```python
if self.mode == "grid":
    # 网格模式：自动检测网格并分割
    grid_regions = self.detect_grid_regions(left, top, right, bottom)
    self.selected_regions.extend(grid_regions)
    
    # 绘制网格
    self.draw_grid_regions(grid_regions)
    
    # 网格模式选择一次就完成
    self.finish_selection()
```

#### 🆕 网格检测算法增强
- **自定义比例**：支持任意横×竖网格比例设置
- **🔄 智能翻转**：自动检测框选区域方向，智能翻转网格比例以获得最佳适配
  - 例如：设置4×10网格，框选10×4区域时自动翻转为10×4网格
  - 基于区域利用率和形状匹配度的智能算法
- **⬜ 自动回正**：所有网格单元自动回正为正方形，确保识别准确性
- **多网格区域**：支持创建分区A、分区B等多个独立网格区域
- **智能推荐**：
  - 数独：推荐 9×9 网格
  - 数织：推荐 15×15 网格
  - 华容道/滑块：推荐 4×4 网格
  - 其他：默认 10×10 网格
- **可视化增强**：
  - 不同区域使用不同颜色边框
  - 显示区域名称、网格比例和翻转状态（🔄图标）
  - 显示正方形单元格尺寸信息
  - 部分单元显示坐标编号
- **区域管理**：
  - 支持单独删除某个网格区域
  - 支持删除选中的单个区域

## 🎮 使用方法

### MuMu窗口管理
1. **启动程序**：自动检测管理员权限
2. **查找窗口**：自动查找MuMu模拟器窗口
3. **设置置顶**：询问是否将MuMu窗口置顶
4. **手动控制**：在"自动化控制"标签页中手动管理

### 区域选择模式
1. **单区域模式**：选择一个区域后自动完成
2. **多区域模式**：可以选择多个独立区域，右键完成
3. **网格模式**：
   - 可设置自定义网格比例（横×竖）
   - 🔄 **智能翻转**：自动适配框选区域方向
   - ⬜ **自动回正**：强制生成正方形网格单元
   - 支持多次选择，创建分区A、分区B等
   - 每个区域用不同颜色区分
   - 支持单独删除某个网格区域

### 操作流程
```
选择游戏类型 → 选择框选模式 → 设置网格比例(网格模式) → 开始框选 → 管理区域 → 保存配置 → 测试识别
```

### 🆕 新增功能操作
1. **网格比例设置**：
   - 在网格模式下，可以设置横×竖的网格比例
   - 输入框支持任意数字，如 9×9、15×15、20×10 等

2. **多网格区域创建**：
   - 网格模式下可以多次选择区域
   - 第一次选择创建"分区A"，第二次创建"分区B"，以此类推
   - 每个区域用不同颜色显示

3. **🔄 智能翻转功能**：
   - 设置网格比例后，程序会自动检测框选区域的方向
   - 如果框选区域与设置比例不匹配，自动翻转以获得最佳适配
   - 翻转后会显示🔄图标和"智能翻转"提示
   - 例如：设置4×10，框选竖长区域时自动变为10×4

4. **⬜ 自动回正功能**：
   - 所有网格单元强制回正为正方形
   - 根据框选区域自动计算最佳正方形尺寸
   - 网格居中显示在框选区域内
   - 显示实际使用的区域尺寸和单元格大小

5. **区域删除管理**：
   - **删除选中**：在区域列表中选择后点击"删除选中"
   - **删除网格区域**：弹出对话框选择要删除的整个网格区域
   - 删除后会显示删除的区域名称和数量

## 🔍 技术实现

### 管理员权限管理
- **检测**：`ctypes.windll.shell32.IsUserAnAdmin()`
- **请求**：`ShellExecuteW` 重新启动程序
- **窗口控制**：`win32gui` API 控制窗口状态

### 窗口管理功能
- **查找窗口**：枚举所有窗口，匹配MuMu相关标题
- **置顶控制**：`SetWindowPos` 设置 `HWND_TOPMOST`
- **前台显示**：`SetForegroundWindow` 带到前台
- **相对坐标**：建立窗口相对坐标系统

### 网格检测算法
- **动态网格**：根据游戏类型自动调整网格大小
- **精确分割**：计算每个网格单元的精确坐标
- **可视化反馈**：实时显示网格边框和编号

## 📁 文件结构

```
├── main.py                     # 主程序入口（管理员权限检测）
├── utils/
│   └── admin_manager.py        # 管理员权限和窗口管理
├── core/
│   └── screen_selector.py      # 屏幕选择器（网格检测）
├── gui/
│   ├── main_window.py          # 主窗口（MuMu窗口控制）
│   └── region_selector.py      # 区域选择器
└── 功能说明.md                 # 本说明文档
```

## 🎯 测试验证

### 窗口管理测试
1. ✅ 启动程序自动检测管理员权限
2. ✅ 自动查找MuMu窗口并设置置顶
3. ✅ 程序退出时自动取消置顶
4. ✅ 手动控制MuMu窗口状态

### 区域选择测试
1. ✅ 单区域模式：选择一次自动完成
2. ✅ 多区域模式：可选择多个区域，右键完成
3. ✅ 网格模式：自动分割成网格，一次完成
4. ✅ 不同游戏类型使用不同网格大小

## 🚀 使用建议

### 推荐配置
- **数独游戏**：使用网格模式，9×9分割，可创建多个分区用于不同难度
- **数织游戏**：
  - 方案1：使用多区域模式，分别选择行提示、列提示区域
  - 方案2：使用网格模式，15×15分割主游戏区域，再用多区域选择提示区域
- **华容道/滑块**：使用网格模式，4×4分割，或单区域模式选择整个游戏区域
- **复杂游戏**：结合使用网格模式（主游戏区域）+ 多区域模式（UI元素）

### 注意事项
- 首次运行需要管理员权限才能控制MuMu窗口
- 网格模式会根据游戏类型自动调整网格大小
- 程序退出时会自动清理窗口设置，无需手动操作

---

**版本**：v1.0  
**更新时间**：2025-01-29  
**状态**：所有功能已测试通过 ✅
