#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理器
负责配置的保存、加载和管理，每个配置对应一个独立的JSON文件
"""

import json
import os
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any

class ConfigManager:
    """配置管理器类"""
    
    def __init__(self, config_dir="data/configs"):
        self.config_dir = config_dir
        self.configs = {}
        self.ensure_config_dir()
        self.load_all_configs()
        
    def ensure_config_dir(self):
        """确保配置目录存在"""
        os.makedirs(self.config_dir, exist_ok=True)
        
    def generate_config_id(self) -> str:
        """生成唯一的配置ID"""
        return str(uuid.uuid4())
        
    def create_config(self, name: str, game_type: str, description: str = "", regions: List[Dict] = None) -> str:
        """创建新配置"""
        if regions is None:
            regions = []
            
        # 检查名称是否已存在
        if self.config_exists(name):
            raise ValueError(f"配置名称 '{name}' 已存在")
            
        # 生成配置ID
        config_id = self.generate_config_id()
        
        # 创建配置数据
        config_data = {
            "id": config_id,
            "name": name,
            "game_type": game_type,
            "description": description,
            "regions": regions,
            "created_time": datetime.now().isoformat(),
            "last_used": "",
            "version": "1.0",
            "metadata": {
                "region_count": len(regions),
                "auto_detect": False,
                "precise_mode": False,
                "relative_coords": True
            }
        }
        
        # 保存配置
        self.save_config(config_id, config_data)
        
        return config_id
        
    def save_config(self, config_id: str, config_data: Dict[str, Any]):
        """保存配置到文件"""
        try:
            # 更新内存中的配置
            self.configs[config_id] = config_data
            
            # 保存到文件
            config_file = os.path.join(self.config_dir, f"{config_id}.json")
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            raise Exception(f"保存配置失败: {e}")
            
    def load_config(self, config_id: str) -> Optional[Dict[str, Any]]:
        """加载指定配置"""
        try:
            config_file = os.path.join(self.config_dir, f"{config_id}.json")
            
            if not os.path.exists(config_file):
                return None
                
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
                
            # 更新内存中的配置
            self.configs[config_id] = config_data
            
            return config_data
            
        except Exception as e:
            print(f"加载配置失败 {config_id}: {e}")
            return None
            
    def load_all_configs(self):
        """加载所有配置"""
        try:
            self.configs.clear()
            
            if not os.path.exists(self.config_dir):
                return
                
            # 遍历配置目录
            for filename in os.listdir(self.config_dir):
                if filename.endswith('.json'):
                    config_id = filename[:-5]  # 去掉.json后缀
                    self.load_config(config_id)
                    
        except Exception as e:
            print(f"加载所有配置失败: {e}")
            
    def get_config(self, config_id: str) -> Optional[Dict[str, Any]]:
        """获取配置"""
        return self.configs.get(config_id)
        
    def get_config_by_name(self, name: str) -> Optional[Dict[str, Any]]:
        """根据名称获取配置"""
        for config_data in self.configs.values():
            if config_data.get('name') == name:
                return config_data
        return None
        
    def get_all_configs(self) -> Dict[str, Dict[str, Any]]:
        """获取所有配置"""
        return self.configs.copy()
        
    def get_configs_by_game_type(self, game_type: str) -> Dict[str, Dict[str, Any]]:
        """根据游戏类型获取配置"""
        filtered_configs = {}
        for config_id, config_data in self.configs.items():
            if config_data.get('game_type') == game_type:
                filtered_configs[config_id] = config_data
        return filtered_configs
        
    def config_exists(self, name: str) -> bool:
        """检查配置是否存在"""
        return self.get_config_by_name(name) is not None
        
    def update_config(self, config_id: str, updates: Dict[str, Any]):
        """更新配置"""
        if config_id not in self.configs:
            raise ValueError(f"配置 {config_id} 不存在")
            
        # 更新配置数据
        config_data = self.configs[config_id]
        config_data.update(updates)
        
        # 更新修改时间
        config_data['last_modified'] = datetime.now().isoformat()
        
        # 保存配置
        self.save_config(config_id, config_data)
        
    def delete_config(self, config_id: str):
        """删除配置"""
        try:
            # 删除文件
            config_file = os.path.join(self.config_dir, f"{config_id}.json")
            if os.path.exists(config_file):
                os.remove(config_file)
                
            # 从内存中删除
            if config_id in self.configs:
                del self.configs[config_id]
                
        except Exception as e:
            raise Exception(f"删除配置失败: {e}")
            
    def copy_config(self, config_id: str, new_name: str) -> str:
        """复制配置"""
        if config_id not in self.configs:
            raise ValueError(f"配置 {config_id} 不存在")
            
        if self.config_exists(new_name):
            raise ValueError(f"配置名称 '{new_name}' 已存在")
            
        # 获取原配置
        original_config = self.configs[config_id]
        
        # 创建新配置
        new_config_id = self.generate_config_id()
        new_config_data = original_config.copy()
        new_config_data['id'] = new_config_id
        new_config_data['name'] = new_name
        new_config_data['created_time'] = datetime.now().isoformat()
        new_config_data['last_used'] = ""
        
        # 保存新配置
        self.save_config(new_config_id, new_config_data)
        
        return new_config_id
        
    def update_last_used(self, config_id: str):
        """更新最后使用时间"""
        if config_id in self.configs:
            self.configs[config_id]['last_used'] = datetime.now().isoformat()
            self.save_config(config_id, self.configs[config_id])
            
    def export_config(self, config_id: str, export_path: str):
        """导出配置"""
        if config_id not in self.configs:
            raise ValueError(f"配置 {config_id} 不存在")
            
        try:
            config_data = self.configs[config_id]
            with open(export_path, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            raise Exception(f"导出配置失败: {e}")
            
    def import_config(self, import_path: str, new_name: str = None) -> str:
        """导入配置"""
        try:
            with open(import_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
                
            # 生成新的配置ID
            new_config_id = self.generate_config_id()
            config_data['id'] = new_config_id
            
            # 处理名称冲突
            original_name = config_data.get('name', '导入的配置')
            if new_name:
                config_data['name'] = new_name
            elif self.config_exists(original_name):
                counter = 1
                while self.config_exists(f"{original_name}_{counter}"):
                    counter += 1
                config_data['name'] = f"{original_name}_{counter}"
                
            # 更新时间
            config_data['created_time'] = datetime.now().isoformat()
            config_data['last_used'] = ""
            
            # 保存配置
            self.save_config(new_config_id, config_data)
            
            return new_config_id
            
        except Exception as e:
            raise Exception(f"导入配置失败: {e}")
            
    def get_config_stats(self) -> Dict[str, Any]:
        """获取配置统计信息"""
        stats = {
            'total_configs': len(self.configs),
            'game_types': {},
            'recent_configs': []
        }
        
        # 统计游戏类型
        for config_data in self.configs.values():
            game_type = config_data.get('game_type', '未知')
            stats['game_types'][game_type] = stats['game_types'].get(game_type, 0) + 1
            
        # 获取最近使用的配置
        recent_configs = sorted(
            self.configs.values(),
            key=lambda x: x.get('last_used', ''),
            reverse=True
        )[:5]
        
        stats['recent_configs'] = [
            {'id': config['id'], 'name': config['name'], 'last_used': config.get('last_used', '')}
            for config in recent_configs
        ]
        
        return stats
