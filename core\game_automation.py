#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
游戏自动化框架
提供游戏操作的基础框架，支持扩展不同游戏类型
"""

import pyautogui
import time
from typing import Dict, List, Tuple, Any, Optional
from abc import ABC, abstractmethod
from dataclasses import dataclass
from enum import Enum

class ActionType(Enum):
    """操作类型枚举"""
    CLICK = "click"
    DRAG = "drag"
    KEY_PRESS = "key_press"
    WAIT = "wait"
    CUSTOM = "custom"

@dataclass
class GameAction:
    """游戏操作数据类"""
    action_type: ActionType
    position: Optional[Tuple[int, int]] = None
    target_position: Optional[Tuple[int, int]] = None
    key: Optional[str] = None
    duration: float = 0.1
    delay_after: float = 0.1
    description: str = ""
    custom_data: Dict[str, Any] = None

class GameAutomationBase(ABC):
    """游戏自动化基类"""
    
    def __init__(self, game_type: str):
        self.game_type = game_type
        self.is_enabled = False
        self.action_queue = []
        self.current_state = {}
        
        # 安全设置
        pyautogui.FAILSAFE = True
        pyautogui.PAUSE = 0.1
        
    @abstractmethod
    def analyze_game_state(self, recognition_result: Dict[str, Any]) -> Dict[str, Any]:
        """分析游戏状态"""
        pass
        
    @abstractmethod
    def generate_solution(self, game_state: Dict[str, Any]) -> List[GameAction]:
        """生成解决方案"""
        pass
        
    @abstractmethod
    def validate_action(self, action: GameAction) -> bool:
        """验证操作的有效性"""
        pass
        
    def enable_automation(self):
        """启用自动化"""
        self.is_enabled = True
        
    def disable_automation(self):
        """禁用自动化"""
        self.is_enabled = False
        self.action_queue.clear()
        
    def add_action(self, action: GameAction):
        """添加操作到队列"""
        if self.validate_action(action):
            self.action_queue.append(action)
            
    def execute_action(self, action: GameAction) -> bool:
        """执行单个操作"""
        try:
            if not self.is_enabled:
                return False
                
            if action.action_type == ActionType.CLICK:
                if action.position:
                    pyautogui.click(action.position[0], action.position[1], duration=action.duration)
                    
            elif action.action_type == ActionType.DRAG:
                if action.position and action.target_position:
                    pyautogui.drag(
                        action.target_position[0] - action.position[0],
                        action.target_position[1] - action.position[1],
                        duration=action.duration,
                        button='left'
                    )
                    
            elif action.action_type == ActionType.KEY_PRESS:
                if action.key:
                    pyautogui.press(action.key)
                    
            elif action.action_type == ActionType.WAIT:
                time.sleep(action.duration)
                
            elif action.action_type == ActionType.CUSTOM:
                # 子类可以重写此方法处理自定义操作
                self.execute_custom_action(action)
                
            # 操作后延迟
            if action.delay_after > 0:
                time.sleep(action.delay_after)
                
            return True
            
        except Exception as e:
            print(f"执行操作失败: {e}")
            return False
            
    def execute_custom_action(self, action: GameAction):
        """执行自定义操作（子类重写）"""
        pass
        
    def execute_action_queue(self) -> bool:
        """执行操作队列"""
        try:
            if not self.is_enabled:
                return False
                
            success_count = 0
            
            for action in self.action_queue:
                if self.execute_action(action):
                    success_count += 1
                else:
                    print(f"操作失败: {action.description}")
                    
            self.action_queue.clear()
            
            return success_count > 0
            
        except Exception as e:
            print(f"执行操作队列失败: {e}")
            return False
            
    def get_action_queue_info(self) -> Dict[str, Any]:
        """获取操作队列信息"""
        return {
            'queue_length': len(self.action_queue),
            'is_enabled': self.is_enabled,
            'game_type': self.game_type,
            'actions': [
                {
                    'type': action.action_type.value,
                    'description': action.description,
                    'position': action.position,
                    'duration': action.duration
                }
                for action in self.action_queue
            ]
        }

class SudokuAutomation(GameAutomationBase):
    """数独自动化"""
    
    def __init__(self):
        super().__init__("sudoku")
        self.grid_positions = {}  # 存储网格位置映射
        
    def analyze_game_state(self, recognition_result: Dict[str, Any]) -> Dict[str, Any]:
        """分析数独游戏状态"""
        try:
            if 'grid' not in recognition_result:
                return {'error': '无法获取数独网格'}
                
            grid = recognition_result['grid']
            
            # 分析空白位置
            empty_cells = []
            for row in range(9):
                for col in range(9):
                    if grid[row][col] == 0:
                        empty_cells.append((row, col))
                        
            # 分析可能的数字
            possible_numbers = self.analyze_possible_numbers(grid)
            
            return {
                'grid': grid,
                'empty_cells': empty_cells,
                'possible_numbers': possible_numbers,
                'is_valid': recognition_result.get('is_valid', False)
            }
            
        except Exception as e:
            return {'error': str(e)}
            
    def analyze_possible_numbers(self, grid: List[List[int]]) -> Dict[Tuple[int, int], List[int]]:
        """分析每个空白位置可能的数字"""
        possible = {}
        
        for row in range(9):
            for col in range(9):
                if grid[row][col] == 0:
                    possible[(row, col)] = self.get_possible_numbers(grid, row, col)
                    
        return possible
        
    def get_possible_numbers(self, grid: List[List[int]], row: int, col: int) -> List[int]:
        """获取指定位置可能的数字"""
        used_numbers = set()
        
        # 检查行
        for c in range(9):
            if grid[row][c] != 0:
                used_numbers.add(grid[row][c])
                
        # 检查列
        for r in range(9):
            if grid[r][col] != 0:
                used_numbers.add(grid[r][col])
                
        # 检查3x3方块
        box_row, box_col = 3 * (row // 3), 3 * (col // 3)
        for r in range(box_row, box_row + 3):
            for c in range(box_col, box_col + 3):
                if grid[r][c] != 0:
                    used_numbers.add(grid[r][c])
                    
        # 返回可能的数字
        return [num for num in range(1, 10) if num not in used_numbers]
        
    def generate_solution(self, game_state: Dict[str, Any]) -> List[GameAction]:
        """生成数独解决方案"""
        actions = []
        
        try:
            if 'error' in game_state:
                return actions
                
            possible_numbers = game_state.get('possible_numbers', {})
            
            # 找到只有一个可能数字的位置
            for (row, col), numbers in possible_numbers.items():
                if len(numbers) == 1:
                    # 生成点击操作
                    if (row, col) in self.grid_positions:
                        position = self.grid_positions[(row, col)]
                        
                        # 点击位置
                        click_action = GameAction(
                            action_type=ActionType.CLICK,
                            position=position,
                            description=f"点击位置({row}, {col})"
                        )
                        actions.append(click_action)
                        
                        # 输入数字
                        key_action = GameAction(
                            action_type=ActionType.KEY_PRESS,
                            key=str(numbers[0]),
                            description=f"输入数字{numbers[0]}"
                        )
                        actions.append(key_action)
                        
        except Exception as e:
            print(f"生成数独解决方案失败: {e}")
            
        return actions
        
    def validate_action(self, action: GameAction) -> bool:
        """验证数独操作"""
        if action.action_type == ActionType.CLICK:
            return action.position is not None
        elif action.action_type == ActionType.KEY_PRESS:
            return action.key in '123456789'
        return True
        
    def set_grid_positions(self, positions: Dict[Tuple[int, int], Tuple[int, int]]):
        """设置网格位置映射"""
        self.grid_positions = positions

class NonogramAutomation(GameAutomationBase):
    """数织自动化"""
    
    def __init__(self):
        super().__init__("nonogram")
        
    def analyze_game_state(self, recognition_result: Dict[str, Any]) -> Dict[str, Any]:
        """分析数织游戏状态"""
        try:
            row_hints = recognition_result.get('row_hints', [])
            col_hints = recognition_result.get('col_hints', [])
            main_grid = recognition_result.get('main_grid', [])
            
            return {
                'row_hints': row_hints,
                'col_hints': col_hints,
                'main_grid': main_grid,
                'grid_size': recognition_result.get('grid_size', (10, 10))
            }
            
        except Exception as e:
            return {'error': str(e)}
            
    def generate_solution(self, game_state: Dict[str, Any]) -> List[GameAction]:
        """生成数织解决方案"""
        # 数织求解算法比较复杂，这里只是框架
        actions = []
        
        # TODO: 实现数织求解逻辑
        
        return actions
        
    def validate_action(self, action: GameAction) -> bool:
        """验证数织操作"""
        return action.action_type in [ActionType.CLICK, ActionType.DRAG]

class HuarongAutomation(GameAutomationBase):
    """华容道自动化"""
    
    def __init__(self):
        super().__init__("huarong")
        
    def analyze_game_state(self, recognition_result: Dict[str, Any]) -> Dict[str, Any]:
        """分析华容道游戏状态"""
        try:
            board = recognition_result.get('board', [])
            piece_positions = recognition_result.get('piece_positions', {})
            
            return {
                'board': board,
                'piece_positions': piece_positions,
                'board_size': recognition_result.get('board_size', (5, 4))
            }
            
        except Exception as e:
            return {'error': str(e)}
            
    def generate_solution(self, game_state: Dict[str, Any]) -> List[GameAction]:
        """生成华容道解决方案"""
        actions = []
        
        # TODO: 实现华容道求解逻辑
        
        return actions
        
    def validate_action(self, action: GameAction) -> bool:
        """验证华容道操作"""
        return action.action_type == ActionType.DRAG

class GameAutomationManager:
    """游戏自动化管理器"""
    
    def __init__(self):
        self.automations = {
            'sudoku': SudokuAutomation(),
            'nonogram': NonogramAutomation(),
            'huarong': HuarongAutomation()
        }
        self.current_automation = None
        
    def get_automation(self, game_type: str) -> Optional[GameAutomationBase]:
        """获取指定游戏的自动化实例"""
        return self.automations.get(game_type)
        
    def set_current_game(self, game_type: str) -> bool:
        """设置当前游戏类型"""
        if game_type in self.automations:
            self.current_automation = self.automations[game_type]
            return True
        return False
        
    def enable_automation(self, game_type: str = None):
        """启用自动化"""
        if game_type:
            automation = self.get_automation(game_type)
            if automation:
                automation.enable_automation()
        elif self.current_automation:
            self.current_automation.enable_automation()
            
    def disable_automation(self, game_type: str = None):
        """禁用自动化"""
        if game_type:
            automation = self.get_automation(game_type)
            if automation:
                automation.disable_automation()
        elif self.current_automation:
            self.current_automation.disable_automation()
            
    def generate_and_execute_solution(self, recognition_result: Dict[str, Any]) -> bool:
        """生成并执行解决方案"""
        if not self.current_automation:
            return False
            
        try:
            # 分析游戏状态
            game_state = self.current_automation.analyze_game_state(recognition_result)
            
            if 'error' in game_state:
                print(f"游戏状态分析失败: {game_state['error']}")
                return False
                
            # 生成解决方案
            actions = self.current_automation.generate_solution(game_state)
            
            if not actions:
                print("没有生成可执行的操作")
                return False
                
            # 添加操作到队列
            for action in actions:
                self.current_automation.add_action(action)
                
            # 执行操作队列
            return self.current_automation.execute_action_queue()
            
        except Exception as e:
            print(f"生成并执行解决方案失败: {e}")
            return False
            
    def get_automation_status(self) -> Dict[str, Any]:
        """获取自动化状态"""
        status = {}
        
        for game_type, automation in self.automations.items():
            status[game_type] = {
                'is_enabled': automation.is_enabled,
                'queue_length': len(automation.action_queue)
            }
            
        return {
            'automations': status,
            'current_game': self.current_automation.game_type if self.current_automation else None
        }
