#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
识别管理器
统一管理不同游戏类型的识别功能
"""

import cv2
import numpy as np
from typing import Dict, Any, List, Tuple, Optional
from core.image_recognizer import ImageRecognizer
from core.game_recognizers import SudokuRecognizer, NonogramRecognizer, HuarongRecognizer
from core.config_manager import ConfigManager
import pyautogui

class RecognitionManager:
    """识别管理器类"""
    
    def __init__(self):
        self.config_manager = ConfigManager()
        
        # 初始化各种识别器
        self.recognizers = {
            'sudoku': SudokuRecognizer(),
            'nonogram': NonogramRecognizer(),
            'huarong': HuarongRecognizer(),
            'klotski': ImageRecognizer(),  # 滑块拼图使用基础识别器
            'custom': ImageRecognizer()    # 自定义使用基础识别器
        }
        
        # 当前配置
        self.current_config = None
        self.current_game_type = None
        
    def load_config(self, config_id: str) -> bool:
        """加载配置"""
        try:
            config = self.config_manager.get_config(config_id)
            if not config:
                return False
                
            self.current_config = config
            self.current_game_type = config.get('game_type', '')
            
            # 更新最后使用时间
            self.config_manager.update_last_used(config_id)
            
            return True
            
        except Exception as e:
            print(f"加载配置失败: {e}")
            return False
            
    def recognize_game(self, config_id: str = None) -> Dict[str, Any]:
        """识别游戏"""
        try:
            # 如果指定了配置ID，先加载配置
            if config_id:
                if not self.load_config(config_id):
                    return {'error': '配置加载失败', 'config_id': config_id}
                    
            if not self.current_config:
                return {'error': '没有加载配置'}
                
            game_type = self.current_game_type
            regions = self.current_config.get('regions', [])
            
            if not regions:
                return {'error': '配置中没有区域信息'}
                
            # 根据游戏类型调用相应的识别方法
            if game_type == 'sudoku':
                return self.recognize_sudoku(regions)
            elif game_type == 'nonogram':
                return self.recognize_nonogram(regions)
            elif game_type == 'huarong':
                return self.recognize_huarong(regions)
            elif game_type == 'klotski':
                return self.recognize_klotski(regions)
            else:
                return self.recognize_custom(regions)
                
        except Exception as e:
            return {'error': str(e)}
            
    def recognize_sudoku(self, regions: List[Dict]) -> Dict[str, Any]:
        """识别数独"""
        try:
            if not regions:
                return {'error': '数独需要至少一个区域'}
                
            # 获取主网格区域
            main_region = regions[0]
            
            # 捕获区域图像
            image = self.capture_region(main_region)
            
            # 使用数独识别器
            recognizer = self.recognizers['sudoku']
            result = recognizer.recognize_sudoku_grid(image)
            
            # 添加区域信息
            result['regions_used'] = [main_region]
            result['config_id'] = self.current_config.get('id', '')
            
            return result
            
        except Exception as e:
            return {'error': str(e), 'game_type': 'sudoku'}
            
    def recognize_nonogram(self, regions: List[Dict]) -> Dict[str, Any]:
        """识别数织"""
        try:
            if len(regions) < 2:
                return {'error': '数织需要至少2个区域（横提示和竖提示）'}
                
            # 获取区域
            row_hints_region = None
            col_hints_region = None
            main_grid_region = None
            
            for region in regions:
                region_name = region.get('name', '').lower()
                if '横' in region_name or 'row' in region_name:
                    row_hints_region = region
                elif '竖' in region_name or 'col' in region_name:
                    col_hints_region = region
                elif '网格' in region_name or 'grid' in region_name:
                    main_grid_region = region
                    
            # 如果没有明确标识，按顺序分配
            if not row_hints_region:
                row_hints_region = regions[0]
            if not col_hints_region:
                col_hints_region = regions[1]
            if not main_grid_region and len(regions) > 2:
                main_grid_region = regions[2]
                
            # 捕获图像
            row_hints_image = self.capture_region(row_hints_region)
            col_hints_image = self.capture_region(col_hints_region)
            
            main_grid_image = None
            if main_grid_region:
                main_grid_image = self.capture_region(main_grid_region)
            else:
                # 如果没有主网格，创建空白图像
                main_grid_image = np.zeros((400, 400, 3), dtype=np.uint8)
                
            # 使用数织识别器
            recognizer = self.recognizers['nonogram']
            result = recognizer.recognize_nonogram(
                main_grid_image, row_hints_image, col_hints_image
            )
            
            # 添加区域信息
            result['regions_used'] = regions[:3]
            result['config_id'] = self.current_config.get('id', '')
            
            return result
            
        except Exception as e:
            return {'error': str(e), 'game_type': 'nonogram'}
            
    def recognize_huarong(self, regions: List[Dict]) -> Dict[str, Any]:
        """识别华容道"""
        try:
            if not regions:
                return {'error': '华容道需要至少一个区域'}
                
            # 获取棋盘区域
            board_region = regions[0]
            
            # 捕获区域图像
            image = self.capture_region(board_region)
            
            # 使用华容道识别器
            recognizer = self.recognizers['huarong']
            result = recognizer.recognize_huarong_board(image)
            
            # 添加区域信息
            result['regions_used'] = [board_region]
            result['config_id'] = self.current_config.get('id', '')
            
            return result
            
        except Exception as e:
            return {'error': str(e), 'game_type': 'huarong'}
            
    def recognize_klotski(self, regions: List[Dict]) -> Dict[str, Any]:
        """识别滑块拼图"""
        try:
            if not regions:
                return {'error': '滑块拼图需要至少一个区域'}
                
            # 获取拼图区域
            puzzle_region = regions[0]
            
            # 捕获区域图像
            image = self.capture_region(puzzle_region)
            
            # 使用基础识别器进行网格识别
            recognizer = self.recognizers['klotski']
            
            # 假设是3x3或4x4的拼图
            grid_size = (3, 3)  # 可以根据配置调整
            grid_results = recognizer.recognize_grid(image, grid_size)
            
            result = {
                'grid_results': grid_results,
                'grid_size': grid_size,
                'regions_used': [puzzle_region],
                'config_id': self.current_config.get('id', ''),
                'game_type': 'klotski'
            }
            
            return result
            
        except Exception as e:
            return {'error': str(e), 'game_type': 'klotski'}
            
    def recognize_custom(self, regions: List[Dict]) -> Dict[str, Any]:
        """识别自定义游戏"""
        try:
            if not regions:
                return {'error': '自定义游戏需要至少一个区域'}
                
            results = []
            recognizer = self.recognizers['custom']
            
            # 对每个区域进行识别
            for i, region in enumerate(regions):
                image = self.capture_region(region)
                
                # 尝试识别为网格
                try:
                    grid_size = (5, 5)  # 默认网格大小
                    grid_result = recognizer.recognize_grid(image, grid_size)
                    
                    region_result = {
                        'region_index': i,
                        'region_name': region.get('name', f'区域{i+1}'),
                        'grid_result': grid_result,
                        'grid_size': grid_size
                    }
                    
                except Exception:
                    # 如果网格识别失败，尝试单个数字识别
                    digit_result = recognizer.recognize_digit(image)
                    
                    region_result = {
                        'region_index': i,
                        'region_name': region.get('name', f'区域{i+1}'),
                        'digit_result': digit_result
                    }
                    
                results.append(region_result)
                
            return {
                'results': results,
                'regions_used': regions,
                'config_id': self.current_config.get('id', ''),
                'game_type': 'custom'
            }
            
        except Exception as e:
            return {'error': str(e), 'game_type': 'custom'}
            
    def capture_region(self, region: Dict) -> np.ndarray:
        """捕获区域图像"""
        try:
            x = region['x']
            y = region['y']
            width = region['width']
            height = region['height']
            
            # 使用pyautogui截图
            screenshot = pyautogui.screenshot(region=(x, y, width, height))
            
            # 转换为OpenCV格式
            image = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
            
            return image
            
        except Exception as e:
            raise Exception(f"捕获区域失败: {e}")
            
    def get_available_configs(self, game_type: str = None) -> List[Dict[str, Any]]:
        """获取可用的配置"""
        try:
            if game_type:
                configs = self.config_manager.get_configs_by_game_type(game_type)
            else:
                configs = self.config_manager.get_all_configs()
                
            # 转换为列表格式
            config_list = []
            for config_id, config_data in configs.items():
                config_list.append({
                    'id': config_id,
                    'name': config_data.get('name', '未命名'),
                    'game_type': config_data.get('game_type', ''),
                    'description': config_data.get('description', ''),
                    'region_count': len(config_data.get('regions', [])),
                    'created_time': config_data.get('created_time', ''),
                    'last_used': config_data.get('last_used', '')
                })
                
            # 按最后使用时间排序
            config_list.sort(key=lambda x: x['last_used'], reverse=True)
            
            return config_list
            
        except Exception as e:
            print(f"获取配置列表失败: {e}")
            return []
            
    def get_current_config_info(self) -> Optional[Dict[str, Any]]:
        """获取当前配置信息"""
        if self.current_config:
            return {
                'id': self.current_config.get('id', ''),
                'name': self.current_config.get('name', ''),
                'game_type': self.current_game_type,
                'region_count': len(self.current_config.get('regions', []))
            }
        return None
        
    def save_recognition_result(self, result: Dict[str, Any], filename: str = None):
        """保存识别结果"""
        try:
            import json
            import os
            from datetime import datetime
            
            if not filename:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"recognition_result_{timestamp}.json"
                
            # 确保结果目录存在
            os.makedirs("data/results", exist_ok=True)
            
            # 保存结果
            result_path = os.path.join("data/results", filename)
            with open(result_path, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
                
            return result_path
            
        except Exception as e:
            print(f"保存识别结果失败: {e}")
            return None
