#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DPI感知和高DPI屏幕适配工具
"""

import sys
import tkinter as tk
from tkinter import ttk

def setup_dpi_awareness():
    """设置DPI感知"""
    try:
        if sys.platform == "win32":
            import ctypes
            # 设置DPI感知
            ctypes.windll.shcore.SetProcessDpiAwareness(1)
    except Exception as e:
        print(f"DPI设置警告: {e}")

def get_dpi_scale(root):
    """获取DPI缩放比例"""
    try:
        # 获取系统DPI
        dpi = root.winfo_fpixels('1i')
        scale = dpi / 96.0  # 96 DPI是标准DPI
        return scale
    except:
        return 1.0

def scale_font_size(base_size, scale=None):
    """根据DPI缩放字体大小"""
    if scale is None:
        scale = 1.0
    return int(base_size * scale)

def configure_styles(root):
    """配置高DPI样式"""
    try:
        scale = get_dpi_scale(root)
        
        # 创建样式
        style = ttk.Style()
        
        # 配置字体大小
        default_font = ('Microsoft YaHei UI', scale_font_size(9, scale))
        heading_font = ('Microsoft YaHei UI', scale_font_size(11, scale), 'bold')
        
        # 配置各种控件样式
        style.configure('TLabel', font=default_font)
        style.configure('TButton', font=default_font, padding=(5*scale, 3*scale))
        style.configure('TEntry', font=default_font)
        style.configure('TCombobox', font=default_font)
        style.configure('TFrame', padding=(5*scale, 5*scale))
        style.configure('TLabelFrame', font=default_font, padding=(10*scale, 5*scale))
        style.configure('TNotebook', padding=(5*scale, 5*scale))
        style.configure('TNotebook.Tab', font=default_font, padding=(10*scale, 5*scale))
        
        # 标题样式
        style.configure('Heading.TLabel', font=heading_font)
        
        return scale
        
    except Exception as e:
        print(f"样式配置警告: {e}")
        return 1.0

class DPIAwareWidget:
    """DPI感知的控件基类"""
    
    def __init__(self, root):
        self.root = root
        self.scale = get_dpi_scale(root)
        
    def scaled(self, value):
        """缩放数值"""
        return int(value * self.scale)
        
    def scaled_tuple(self, *values):
        """缩放元组"""
        return tuple(self.scaled(v) for v in values)
