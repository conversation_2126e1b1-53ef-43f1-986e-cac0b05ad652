#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
游戏插件系统
支持动态添加新游戏类型的识别和自动化功能
"""

import os
import importlib
import inspect
from typing import Dict, List, Any, Type, Optional
from abc import ABC, abstractmethod
from dataclasses import dataclass
from core.image_recognizer import ImageRecognizer
from core.game_automation import Game<PERSON>utomationBase

@dataclass
class GamePluginInfo:
    """游戏插件信息"""
    name: str
    display_name: str
    version: str
    author: str
    description: str
    recognizer_class: Optional[Type[ImageRecognizer]] = None
    automation_class: Optional[Type[GameAutomationBase]] = None
    config_schema: Dict[str, Any] = None
    supported_features: List[str] = None

class GamePlugin(ABC):
    """游戏插件基类"""
    
    @abstractmethod
    def get_plugin_info(self) -> GamePluginInfo:
        """获取插件信息"""
        pass
        
    @abstractmethod
    def get_recognizer_class(self) -> Type[ImageRecognizer]:
        """获取识别器类"""
        pass
        
    @abstractmethod
    def get_automation_class(self) -> Type[GameAutomationBase]:
        """获取自动化类"""
        pass
        
    def get_config_schema(self) -> Dict[str, Any]:
        """获取配置模式（可选）"""
        return {}
        
    def get_supported_features(self) -> List[str]:
        """获取支持的功能列表（可选）"""
        return ['recognition', 'automation']
        
    def validate_plugin(self) -> bool:
        """验证插件有效性（可选）"""
        return True

class GamePluginManager:
    """游戏插件管理器"""
    
    def __init__(self, plugin_dir: str = "plugins"):
        self.plugin_dir = plugin_dir
        self.loaded_plugins = {}
        self.plugin_instances = {}
        
        # 确保插件目录存在
        os.makedirs(plugin_dir, exist_ok=True)
        
        # 创建__init__.py文件
        init_file = os.path.join(plugin_dir, "__init__.py")
        if not os.path.exists(init_file):
            with open(init_file, 'w') as f:
                f.write("# Game plugins directory\n")
                
    def scan_plugins(self) -> List[str]:
        """扫描插件目录"""
        plugins = []
        
        try:
            if not os.path.exists(self.plugin_dir):
                return plugins
                
            for item in os.listdir(self.plugin_dir):
                item_path = os.path.join(self.plugin_dir, item)
                
                # 检查Python文件
                if item.endswith('.py') and item != '__init__.py':
                    plugins.append(item[:-3])  # 移除.py扩展名
                    
                # 检查插件目录
                elif os.path.isdir(item_path) and not item.startswith('__'):
                    init_file = os.path.join(item_path, '__init__.py')
                    if os.path.exists(init_file):
                        plugins.append(item)
                        
        except Exception as e:
            print(f"扫描插件失败: {e}")
            
        return plugins
        
    def load_plugin(self, plugin_name: str) -> bool:
        """加载单个插件"""
        try:
            # 构建模块名
            module_name = f"{self.plugin_dir}.{plugin_name}"
            
            # 动态导入模块
            module = importlib.import_module(module_name)
            
            # 查找插件类
            plugin_class = None
            for name, obj in inspect.getmembers(module):
                if (inspect.isclass(obj) and 
                    issubclass(obj, GamePlugin) and 
                    obj != GamePlugin):
                    plugin_class = obj
                    break
                    
            if not plugin_class:
                print(f"插件 {plugin_name} 中没有找到有效的插件类")
                return False
                
            # 创建插件实例
            plugin_instance = plugin_class()
            
            # 验证插件
            if not plugin_instance.validate_plugin():
                print(f"插件 {plugin_name} 验证失败")
                return False
                
            # 获取插件信息
            plugin_info = plugin_instance.get_plugin_info()
            
            # 存储插件
            self.loaded_plugins[plugin_name] = plugin_info
            self.plugin_instances[plugin_name] = plugin_instance
            
            print(f"成功加载插件: {plugin_info.display_name} v{plugin_info.version}")
            return True
            
        except Exception as e:
            print(f"加载插件 {plugin_name} 失败: {e}")
            return False
            
    def load_all_plugins(self) -> int:
        """加载所有插件"""
        plugins = self.scan_plugins()
        loaded_count = 0
        
        for plugin_name in plugins:
            if self.load_plugin(plugin_name):
                loaded_count += 1
                
        print(f"共加载 {loaded_count} 个插件")
        return loaded_count
        
    def unload_plugin(self, plugin_name: str) -> bool:
        """卸载插件"""
        try:
            if plugin_name in self.loaded_plugins:
                del self.loaded_plugins[plugin_name]
                
            if plugin_name in self.plugin_instances:
                del self.plugin_instances[plugin_name]
                
            return True
            
        except Exception as e:
            print(f"卸载插件 {plugin_name} 失败: {e}")
            return False
            
    def get_plugin_info(self, plugin_name: str) -> Optional[GamePluginInfo]:
        """获取插件信息"""
        return self.loaded_plugins.get(plugin_name)
        
    def get_plugin_instance(self, plugin_name: str) -> Optional[GamePlugin]:
        """获取插件实例"""
        return self.plugin_instances.get(plugin_name)
        
    def get_all_plugins(self) -> Dict[str, GamePluginInfo]:
        """获取所有已加载的插件"""
        return self.loaded_plugins.copy()
        
    def get_plugins_by_feature(self, feature: str) -> List[str]:
        """根据功能获取插件列表"""
        plugins = []
        
        for plugin_name, plugin_info in self.loaded_plugins.items():
            if plugin_info.supported_features and feature in plugin_info.supported_features:
                plugins.append(plugin_name)
                
        return plugins
        
    def create_recognizer(self, plugin_name: str) -> Optional[ImageRecognizer]:
        """创建识别器实例"""
        try:
            plugin_instance = self.get_plugin_instance(plugin_name)
            if not plugin_instance:
                return None
                
            recognizer_class = plugin_instance.get_recognizer_class()
            if recognizer_class:
                return recognizer_class()
                
        except Exception as e:
            print(f"创建识别器失败: {e}")
            
        return None
        
    def create_automation(self, plugin_name: str) -> Optional[GameAutomationBase]:
        """创建自动化实例"""
        try:
            plugin_instance = self.get_plugin_instance(plugin_name)
            if not plugin_instance:
                return None
                
            automation_class = plugin_instance.get_automation_class()
            if automation_class:
                return automation_class()
                
        except Exception as e:
            print(f"创建自动化实例失败: {e}")
            
        return None
        
    def validate_all_plugins(self) -> Dict[str, bool]:
        """验证所有插件"""
        results = {}
        
        for plugin_name, plugin_instance in self.plugin_instances.items():
            try:
                results[plugin_name] = plugin_instance.validate_plugin()
            except Exception as e:
                print(f"验证插件 {plugin_name} 时出错: {e}")
                results[plugin_name] = False
                
        return results
        
    def get_plugin_statistics(self) -> Dict[str, Any]:
        """获取插件统计信息"""
        total_plugins = len(self.loaded_plugins)
        
        feature_count = {}
        for plugin_info in self.loaded_plugins.values():
            if plugin_info.supported_features:
                for feature in plugin_info.supported_features:
                    feature_count[feature] = feature_count.get(feature, 0) + 1
                    
        return {
            'total_plugins': total_plugins,
            'feature_distribution': feature_count,
            'plugin_list': list(self.loaded_plugins.keys())
        }
        
    def export_plugin_info(self, filename: str = "plugin_info.json") -> bool:
        """导出插件信息"""
        try:
            import json
            from datetime import datetime
            
            export_data = {
                'export_time': datetime.now().isoformat(),
                'total_plugins': len(self.loaded_plugins),
                'plugins': {}
            }
            
            for plugin_name, plugin_info in self.loaded_plugins.items():
                export_data['plugins'][plugin_name] = {
                    'name': plugin_info.name,
                    'display_name': plugin_info.display_name,
                    'version': plugin_info.version,
                    'author': plugin_info.author,
                    'description': plugin_info.description,
                    'supported_features': plugin_info.supported_features,
                    'has_recognizer': plugin_info.recognizer_class is not None,
                    'has_automation': plugin_info.automation_class is not None
                }
                
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)
                
            return True
            
        except Exception as e:
            print(f"导出插件信息失败: {e}")
            return False

# 示例插件模板
class ExampleGamePlugin(GamePlugin):
    """示例游戏插件"""
    
    def get_plugin_info(self) -> GamePluginInfo:
        return GamePluginInfo(
            name="example_game",
            display_name="示例游戏",
            version="1.0.0",
            author="示例作者",
            description="这是一个示例游戏插件",
            supported_features=["recognition", "automation"]
        )
        
    def get_recognizer_class(self) -> Type[ImageRecognizer]:
        # 返回自定义识别器类
        return ImageRecognizer
        
    def get_automation_class(self) -> Type[GameAutomationBase]:
        # 返回自定义自动化类
        from core.game_automation import GameAutomationBase
        
        class ExampleAutomation(GameAutomationBase):
            def __init__(self):
                super().__init__("example_game")
                
            def analyze_game_state(self, recognition_result):
                return {}
                
            def generate_solution(self, game_state):
                return []
                
            def validate_action(self, action):
                return True
                
        return ExampleAutomation
        
    def validate_plugin(self) -> bool:
        # 执行插件验证逻辑
        return True

def create_plugin_template(plugin_name: str, display_name: str, author: str) -> str:
    """创建插件模板代码"""
    template = f'''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
{display_name} 游戏插件
作者: {author}
"""

from core.game_plugin_system import GamePlugin, GamePluginInfo
from core.image_recognizer import ImageRecognizer
from core.game_automation import GameAutomationBase, GameAction, ActionType
from typing import Dict, List, Any, Type

class {plugin_name.title()}Recognizer(ImageRecognizer):
    """
    {display_name} 识别器
    """
    
    def __init__(self):
        super().__init__()
        # 添加游戏特定的识别参数
        
    def recognize_game(self, image):
        """识别游戏状态"""
        # 实现游戏特定的识别逻辑
        pass

class {plugin_name.title()}Automation(GameAutomationBase):
    """
    {display_name} 自动化
    """
    
    def __init__(self):
        super().__init__("{plugin_name}")
        
    def analyze_game_state(self, recognition_result: Dict[str, Any]) -> Dict[str, Any]:
        """分析游戏状态"""
        # 实现游戏状态分析逻辑
        return {{}}
        
    def generate_solution(self, game_state: Dict[str, Any]) -> List[GameAction]:
        """生成解决方案"""
        # 实现解决方案生成逻辑
        return []
        
    def validate_action(self, action: GameAction) -> bool:
        """验证操作"""
        # 实现操作验证逻辑
        return True

class {plugin_name.title()}Plugin(GamePlugin):
    """
    {display_name} 插件
    """
    
    def get_plugin_info(self) -> GamePluginInfo:
        return GamePluginInfo(
            name="{plugin_name}",
            display_name="{display_name}",
            version="1.0.0",
            author="{author}",
            description="{display_name} 游戏插件",
            supported_features=["recognition", "automation"]
        )
        
    def get_recognizer_class(self) -> Type[ImageRecognizer]:
        return {plugin_name.title()}Recognizer
        
    def get_automation_class(self) -> Type[GameAutomationBase]:
        return {plugin_name.title()}Automation
        
    def validate_plugin(self) -> bool:
        # 执行插件验证
        return True
'''
    
    return template
